apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: twentycrm
  namespace: twentycrm
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Forwarded-For $http_x_forwarded_for";
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: nginx
  rules:
    - host: crm.example.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: twentycrm-server
                port:
                  name: http-tcp
