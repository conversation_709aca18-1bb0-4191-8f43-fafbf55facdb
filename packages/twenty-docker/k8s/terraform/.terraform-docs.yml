formatter: "markdown table" # this is required

version: ""

header-from: main.tf

recursive:
  enabled: false
  path: modules

output:
  file: "README.md"
  mode: inject
  template: |-
    <!-- BEGIN_TF_DOCS -->
    # TwentyCRM Terraform Docs

    This file was generated by [terraform-docs](https://terraform-docs.io/), for more information on how to install, configure, and use visit their website.

    To update this `README.md` after changes to the Terraform code in this folder, run: `terraform-docs -c `./.terraform-docs.yml .`

    To make configuration changes to how this doc is generated, see `./.terraform-docs.yml`

    {{ .Content }}
    <!-- END_TF_DOCS -->

output-values:
  enabled: false
  from: "outputs.tf"

sort:
  enabled: true
  by: required

settings:
  anchor: true
  color: true
  default: true
  description: true
  escape: true
  hide-empty: true
  html: true
  indent: 2
  lockfile: true
  read-comments: true
  required: true
  sensitive: true
  type: true
