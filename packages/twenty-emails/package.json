{"name": "twenty-emails", "description": "", "author": "", "private": true, "license": "AGPL-3.0", "main": "./dist/index.js", "scripts": {"build": "npx vite build"}, "dependencies": {"@lingui/core": "^5.1.2", "@lingui/react": "^5.1.2", "twenty-shared": "workspace:*"}, "peerDependencies": {"react": "^18.2.0 || ^19.0.0", "react-dom": "^18.2.0 || ^19.0.0"}, "devDependencies": {"@lingui/cli": "^5.1.2", "@lingui/swc-plugin": "^5.1.0", "@lingui/vite-plugin": "^5.1.2", "@types/react": "^19", "@types/react-dom": "^19", "react-email": "4.0.3"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "engines": {"node": "^22.12.0", "npm": "please-use-yarn", "yarn": "^4.0.2"}}