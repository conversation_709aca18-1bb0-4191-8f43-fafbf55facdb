{"name": "twenty-emails", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "tags": ["scope:backend"], "targets": {"build": {"outputs": ["{options.outputPath}"], "options": {"outputPath": "{projectRoot}/dist"}, "dependsOn": ["^build"]}, "start": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}", "command": "email dev -d src/emails -p 4001"}}, "typecheck": {}, "lint": {"options": {"lintFilePatterns": ["{projectRoot}/src/**/*.{ts,tsx,json}", "{projectRoot}/package.json"], "reportUnusedDisableDirectives": "error"}, "configurations": {"fix": {}}}, "fmt": {"options": {"files": "src"}, "configurations": {"fix": {}}}, "lingui:extract": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}", "command": "lingui extract --overwrite"}}, "lingui:compile": {"executor": "nx:run-commands", "options": {"cwd": "{projectRoot}", "command": "lingui compile --typescript"}}}}