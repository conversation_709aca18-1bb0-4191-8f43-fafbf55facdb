/*eslint-disable*/import type{Messages}from"@lingui/core";export const messages=JSON.parse("{\"Suspended Workspace\":[\"Śũśƥēńďēď Ŵōŕķśƥàćē\"],\"Dear {userName},\":[\"Ďēàŕ \",[\"userName\"],\",\"],\"Hello,\":[\"Ĥēĺĺō,\"],\"It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days.\":[\"Ĩţ àƥƥēàŕś ţĥàţ ŷōũŕ ŵōŕķśƥàćē <0>\",[\"workspaceDisplayName\"],\"</0> ĥàś ƀēēń śũśƥēńďēď ƒōŕ \",[\"daysSinceInactive\"],\" ďàŷś.\"],\"The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted.\":[\"Ţĥē ŵōŕķśƥàćē ŵĩĺĺ ƀē ďēàćţĩvàţēď ĩń \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\", àńď àĺĺ ĩţś ďàţà ŵĩĺĺ ƀē ďēĺēţēď.\"],\"If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}.\":[\"Ĩƒ ŷōũ ŵĩśĥ ţō ćōńţĩńũē ũśĩńĝ Ţŵēńţŷ, ƥĺēàśē ũƥďàţē ŷōũŕ śũƀśćŕĩƥţĩōń ŵĩţĥĩń ţĥē ńēxţ \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"Update your subscription\":[\"Ũƥďàţē ŷōũŕ śũƀśćŕĩƥţĩōń\"],\"Validate domain\":[\"Vàĺĩďàţē ďōḿàĩń\"],\"{senderName} (<0>{senderEmail}</0>): Please validate this domain to allow users with <1>@{domain}</1> email addresses to join your workspace without requiring an invitation.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>): Ƥĺēàśē vàĺĩďàţē ţĥĩś ďōḿàĩń ţō àĺĺōŵ ũśēŕś ŵĩţĥ <1>@\",[\"domain\"],\"</1> ēḿàĩĺ àďďŕēśśēś ţō Ĵōĩń ŷōũŕ ŵōŕķśƥàćē ŵĩţĥōũţ ŕēǫũĩŕĩńĝ àń ĩńvĩţàţĩōń.\"],\"Test email\":[\"Ţēśţ ēḿàĩĺ\"],\"Join your team on Twenty\":[\"ĵōĩń ŷōũŕ ţēàḿ ōń Ţŵēńţŷ\"],\"{senderName} (<0>{senderEmail}</0>) has invited you to join a workspace called <1>{workspaceName}</1>.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>) ĥàś ĩńvĩţēď ŷōũ ţō Ĵōĩń à ŵōŕķśƥàćē ćàĺĺēď <1>\",[\"workspaceName\"],\"</1>.\"],\"Accept invite\":[\"Àććēƥţ ĩńvĩţē\"],\"Confirm your email address\":[\"Ćōńƒĩŕḿ ŷōũŕ ēḿàĩĺ àďďŕēśś\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address.\":[\"Ţĥàńķś ƒōŕ ŕēĝĩśţēŕĩńĝ ƒōŕ àń àććōũńţ ōń Ţŵēńţŷ! ßēƒōŕē ŵē ĝēţ śţàŕţēď, ŵē Ĵũśţ ńēēď ţō ćōńƒĩŕḿ ţĥàţ ţĥĩś ĩś ŷōũ. Ćĺĩćķ ƀēĺōŵ ţō vēŕĩƒŷ ŷōũŕ ēḿàĩĺ àďďŕēśś.\"],\"Verify Email\":[\"Vēŕĩƒŷ Ēḿàĩĺ\"],\"Password updated\":[\"Ƥàśśŵōŕď ũƥďàţēď\"],\"This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.\":[\"Ţĥĩś ĩś à ćōńƒĩŕḿàţĩōń ţĥàţ ƥàśśŵōŕď ƒōŕ ŷōũŕ àććōũńţ (\",[\"email\"],\") ŵàś śũććēśśƒũĺĺŷ ćĥàńĝēď ōń \",[\"formattedDate\"],\".\"],\"If you did not initiate this change, please contact your workspace owner immediately.\":[\"Ĩƒ ŷōũ ďĩď ńōţ ĩńĩţĩàţē ţĥĩś ćĥàńĝē, ƥĺēàśē ćōńţàćţ ŷōũŕ ŵōŕķśƥàćē ōŵńēŕ ĩḿḿēďĩàţēĺŷ.\"],\"Connect to Twenty\":[\"Ćōńńēćţ ţō Ţŵēńţŷ\"],\"Reset your password 🗝\":[\"Ŕēśēţ ŷōũŕ ƥàśśŵōŕď 🗝\"],\"This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:\":[\"Ţĥĩś ĺĩńķ ĩś ōńĺŷ vàĺĩď ƒōŕ ţĥē ńēxţ \",[\"duration\"],\". Ĩƒ ţĥē ĺĩńķ ďōēś ńōţ ŵōŕķ, ŷōũ ćàń ũśē ţĥē ĺōĝĩń vēŕĩƒĩćàţĩōń ĺĩńķ ďĩŕēćţĺŷ:\"],\"Reset\":[\"Ŕēśēţ\"],\"Deleted Workspace\":[\"Ďēĺēţēď Ŵōŕķśƥàćē\"],\"Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago.\":[\"Ŷōũŕ ŵōŕķśƥàćē <0>\",[\"workspaceDisplayName\"],\"</0> ĥàś ƀēēń ďēĺēţēď àś ŷōũŕ śũƀśćŕĩƥţĩōń ēxƥĩŕēď \",[\"daysSinceInactive\"],\" ďàŷś àĝō.\"],\"All data in this workspace has been permanently deleted.\":[\"Àĺĺ ďàţà ĩń ţĥĩś ŵōŕķśƥàćē ĥàś ƀēēń ƥēŕḿàńēńţĺŷ ďēĺēţēď.\"],\"If you wish to use Twenty again, you can create a new workspace.\":[\"Ĩƒ ŷōũ ŵĩśĥ ţō ũśē Ţŵēńţŷ àĝàĩń, ŷōũ ćàń ćŕēàţē à ńēŵ ŵōŕķśƥàćē.\"],\"Create a new workspace\":[\"Ćŕēàţē à ńēŵ ŵōŕķśƥàćē\"],\"What is Twenty?\":[\"Ŵĥàţ ĩś Ţŵēńţŷ?\"],\"It's a CRM, a software to help businesses manage their customer data and relationships efficiently.\":[\"Ĩţ'ś à ĆŔḾ, à śōƒţŵàŕē ţō ĥēĺƥ ƀũśĩńēśśēś ḿàńàĝē ţĥēĩŕ ćũśţōḿēŕ ďàţà àńď ŕēĺàţĩōńśĥĩƥś ēƒƒĩćĩēńţĺŷ.\"],\"Website\":[\"Ŵēƀśĩţē\"],\"Visit Twenty's website\":[\"Vĩśĩţ Ţŵēńţŷ'ś ŵēƀśĩţē\"],\"Github\":[\"Ĝĩţĥũƀ\"],\"Visit Twenty's GitHub repository\":[\"Vĩśĩţ Ţŵēńţŷ'ś ĜĩţĤũƀ ŕēƥōśĩţōŕŷ\"],\"User guide\":[\"Ũśēŕ ĝũĩďē\"],\"Read Twenty's user guide\":[\"Ŕēàď Ţŵēńţŷ'ś ũśēŕ ĝũĩďē\"],\"Developers\":[\"Ďēvēĺōƥēŕś\"],\"Visit Twenty's developer documentation\":[\"Vĩśĩţ Ţŵēńţŷ'ś ďēvēĺōƥēŕ ďōćũḿēńţàţĩōń\"],\"Twenty.com, Public Benefit Corporation\":[\"Ţŵēńţŷ.ćōḿ, Ƥũƀĺĩć ßēńēƒĩţ Ćōŕƥōŕàţĩōń\"],\"San Francisco / Paris\":[\"Śàń Ƒŕàńćĩśćō / Ƥàŕĩś\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\":[\"Ţĥàńķś ƒōŕ ŕēĝĩśţēŕĩńĝ ƒōŕ àń àććōũńţ ōń Ţŵēńţŷ! ßēƒōŕē ŵē ĝēţ śţàŕţēď, ŵē Ĵũśţ ńēēď ţō ćōńƒĩŕḿ ţĥàţ ţĥĩś ĩś ŷōũ. Ćĺĩćķ àƀōvē ţō vēŕĩƒŷ ŷōũŕ ēḿàĩĺ àďďŕēśś.\"],\"4WPI3S\":[\"Àććēƥţ ĩńvĩţē\"],\"Yxj+Uc\":[\"Àĺĺ ďàţà ĩń ţĥĩś ŵōŕķśƥàćē ĥàś ƀēēń ƥēŕḿàńēńţĺŷ ďēĺēţēď.\"],\"RPHFhC\":[\"Ćōńƒĩŕḿ ŷōũŕ ēḿàĩĺ àďďŕēśś\"],\"nvkBPN\":[\"Ćōńńēćţ ţō Ţŵēńţŷ\"],\"jPQSEz\":[\"Ćŕēàţē à ńēŵ ŵōŕķśƥàćē\"],\"JRzgV7\":[\"Ďēàŕ\"],\"Lm5BBI\":[\"Ďēàŕ \",[\"userName\"]],\"lIdkf2\":[\"Ďēàŕ \",[\"userName\"],\",\"],\"NTwcnq\":[\"Ďēĺēţēď Ŵōŕķśƥàćē\"],\"S3uuQj\":[\"ēḿàĩĺ àďďŕēśśēś ţō Ĵōĩń ŷōũŕ ŵōŕķśƥàćē ŵĩţĥōũţ ŕēǫũĩŕĩńĝ àń ĩńvĩţàţĩōń.\"],\"tGme7M\":[\"ĥàś ĩńvĩţēď ŷōũ ţō Ĵōĩń à ŵōŕķśƥàćē ćàĺĺēď \"],\"uzTaYi\":[\"Ĥēĺĺō\"],\"Xa0d85\":[\"Ĥēĺĺō,\"],\"eE1nG1\":[\"Ĩƒ ŷōũ ďĩď ńōţ ĩńĩţĩàţē ţĥĩś ćĥàńĝē, ƥĺēàśē ćōńţàćţ ŷōũŕ ŵōŕķśƥàćē ōŵńēŕ ĩḿḿēďĩàţēĺŷ.\"],\"Gz91L8\":[\"Ĩƒ ŷōũ ŵĩśĥ ţō ćōńţĩńũē ũśĩńĝ Ţŵēńţŷ, ƥĺēàśē ũƥďàţē ŷōũŕ śũƀśćŕĩƥţĩōń ŵĩţĥĩń ţĥē ńēxţ \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"0weyko\":[\"Ĩƒ ŷōũ ŵĩśĥ ţō ũśē Ţŵēńţŷ àĝàĩń, ŷōũ ćàń ćŕēàţē à ńēŵ ŵōŕķśƥàćē.\"],\"7JuhZQ\":[\"Ĩţ àƥƥēàŕś ţĥàţ ŷōũŕ ŵōŕķśƥàćē <0>\",[\"workspaceDisplayName\"],\"</0> ĥàś ƀēēń śũśƥēńďēď ƒōŕ \",[\"daysSinceInactive\"],\" ďàŷś.\"],\"PviVyk\":[\"ĵōĩń ŷōũŕ ţēàḿ ōń Ţŵēńţŷ\"],\"ogtYkT\":[\"Ƥàśśŵōŕď ũƥďàţēď\"],\"Yucjaa\":[\"Ƥĺēàśē vàĺĩďàţē ţĥĩś ďōḿàĩń ţō àĺĺōŵ ũśēŕś ŵĩţĥ\"],\"u3Ns4p\":[\"Ƥĺēàśē vàĺĩďàţē ţĥĩś ďōḿàĩń ţō àĺĺōŵ ũśēŕś ŵĩţĥ <0>@\",[\"domain\"],\"</0> ēḿàĩĺ àďďŕēśśēś ţō Ĵōĩń ŷōũŕ ŵōŕķśƥàćē ŵĩţĥōũţ ŕēǫũĩŕĩńĝ àń ĩńvĩţàţĩōń.\"],\"OfhWJH\":[\"Ŕēśēţ\"],\"RE5NiU\":[\"Ŕēśēţ ŷōũŕ ƥàśśŵōŕď 🗝\"],\"UBadaJ\":[\"Śũśƥēńďēď Ŵōŕķśƥàćē \"],\"7yDt8q\":[\"Ţĥàńķś ƒōŕ ŕēĝĩśţēŕĩńĝ ƒōŕ àń àććōũńţ ōń Ţŵēńţŷ! ßēƒōŕē ŵē ĝēţ śţàŕţēď, ŵē Ĵũśţ ńēēď ţō ćōńƒĩŕḿ ţĥàţ ţĥĩś ĩś ŷōũ. Ćĺĩćķ àƀōvē ţō vēŕĩƒŷ ŷōũŕ ēḿàĩĺ àďďŕēśś.\"],\"igorB1\":[\"Ţĥē ŵōŕķśƥàćē ŵĩĺĺ ƀē ďēàćţĩvàţēď ĩń \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\", àńď àĺĺ ĩţś ďàţà ŵĩĺĺ ƀē ďēĺēţēď.\"],\"7OEHy1\":[\"Ţĥĩś ĩś à ćōńƒĩŕḿàţĩōń ţĥàţ ƥàśśŵōŕď ƒōŕ ŷōũŕ àććōũńţ (\",[\"email\"],\") ŵàś śũććēśśƒũĺĺŷ ćĥàńĝēď ōń \",[\"formattedDate\"],\".\"],\"wSOsS+\":[\"Ţĥĩś ĩś à ćōńƒĩŕḿàţĩōń ţĥàţ ƥàśśŵōŕď ƒōŕ ŷōũŕ àććōũńţ (\",[\"email\"],\") ŵàś śũććēśśƒũĺĺŷ ćĥàńĝēď ōń \",[\"formattedDate\"],\".<0/><1/>Ĩƒ ŷōũ ďĩď ńōţ ĩńĩţĩàţē ţĥĩś ćĥàńĝē, ƥĺēàśē ćōńţàćţ ŷōũŕ ŵōŕķśƥàćē ōŵńēŕ ĩḿḿēďĩàţēĺŷ.\"],\"R4gMjN\":[\"Ţĥĩś ĺĩńķ ĩś ōńĺŷ vàĺĩď ƒōŕ ţĥē ńēxţ \",[\"duration\"],\". Ĩƒ ţĥē ĺĩńķ ďōēś ńōţ ŵōŕķ, ŷōũ ćàń ũśē ţĥē ĺōĝĩń vēŕĩƒĩćàţĩōń ĺĩńķ ďĩŕēćţĺŷ:\"],\"2oA637\":[\"Ţĥĩś ĺĩńķ ĩś ōńĺŷ vàĺĩď ƒōŕ ţĥē ńēxţ \",[\"duration\"],\". Ĩƒ ţĥē ĺĩńķ ďōēś ńōţ ŵōŕķ, ŷōũ ćàń ũśē ţĥē ĺōĝĩń vēŕĩƒĩćàţĩōń ĺĩńķ ďĩŕēćţĺŷ:<0/>\"],\"H0v4yC\":[\"Ũƥďàţē ŷōũŕ śũƀśćŕĩƥţĩōń\"],\"QbiUqd\":[\"Vàĺĩďàţē ďōḿàĩń\"],\"wCKkSr\":[\"Vēŕĩƒŷ Ēḿàĩĺ\"],\"9MqLGX\":[\"Ŷōũŕ ŵōŕķśƥàćē <0>\",[\"workspaceDisplayName\"],\"</0> ĥàś ƀēēń ďēĺēţēď àś ŷōũŕ śũƀśćŕĩƥţĩōń ēxƥĩŕēď \",[\"daysSinceInactive\"],\" ďàŷś àĝō.\"],\"KFmFrQ\":[\"Ŷōũŕ ŵōŕķśƥàćē <0>\",[\"workspaceDisplayName\"],\"</0> ĥàś ƀēēń ďēĺēţēď àś ŷōũŕ śũƀśćŕĩƥţĩōń ēxƥĩŕēď \",[\"inactiveDaysBeforeDelete\"],\" ďàŷś àĝō.\"]}")as Messages;