/*eslint-disable*/import type{Messages}from"@lingui/core";export const messages=JSON.parse("{\"Suspended Workspace\":[\"Suspended Workspace\"],\"Dear {userName},\":[\"Dear \",[\"userName\"],\",\"],\"Hello,\":[\"Hello,\"],\"It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days.\":[\"It appears that your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been suspended for \",[\"daysSinceInactive\"],\" days.\"],\"The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted.\":[\"The workspace will be deactivated in \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\", and all its data will be deleted.\"],\"If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}.\":[\"If you wish to continue using Twenty, please update your subscription within the next \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"Update your subscription\":[\"Update your subscription\"],\"Validate domain\":[\"Validate domain\"],\"{senderName} (<0>{senderEmail}</0>): Please validate this domain to allow users with <1>@{domain}</1> email addresses to join your workspace without requiring an invitation.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>): Please validate this domain to allow users with <1>@\",[\"domain\"],\"</1> email addresses to join your workspace without requiring an invitation.\"],\"Test email\":[\"Test email\"],\"Join your team on Twenty\":[\"Join your team on Twenty\"],\"{senderName} (<0>{senderEmail}</0>) has invited you to join a workspace called <1>{workspaceName}</1>.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>) has invited you to join a workspace called <1>\",[\"workspaceName\"],\"</1>.\"],\"Accept invite\":[\"Accept invite\"],\"Confirm your email address\":[\"Confirm your email address\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address.\":[\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address.\"],\"Verify Email\":[\"Verify Email\"],\"Password updated\":[\"Password updated\"],\"This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.\":[\"This is a confirmation that password for your account (\",[\"email\"],\") was successfully changed on \",[\"formattedDate\"],\".\"],\"If you did not initiate this change, please contact your workspace owner immediately.\":[\"If you did not initiate this change, please contact your workspace owner immediately.\"],\"Connect to Twenty\":[\"Connect to Twenty\"],\"Reset your password 🗝\":[\"Reset your password 🗝\"],\"This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:\":[\"This link is only valid for the next \",[\"duration\"],\". If the link does not work, you can use the login verification link directly:\"],\"Reset\":[\"Reset\"],\"Deleted Workspace\":[\"Deleted Workspace\"],\"Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago.\":[\"Your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been deleted as your subscription expired \",[\"daysSinceInactive\"],\" days ago.\"],\"All data in this workspace has been permanently deleted.\":[\"All data in this workspace has been permanently deleted.\"],\"If you wish to use Twenty again, you can create a new workspace.\":[\"If you wish to use Twenty again, you can create a new workspace.\"],\"Create a new workspace\":[\"Create a new workspace\"],\"What is Twenty?\":[\"What is Twenty?\"],\"It's a CRM, a software to help businesses manage their customer data and relationships efficiently.\":[\"It's a CRM, a software to help businesses manage their customer data and relationships efficiently.\"],\"Website\":[\"Website\"],\"Visit Twenty's website\":[\"Visit Twenty's website\"],\"Github\":[\"Github\"],\"Visit Twenty's GitHub repository\":[\"Visit Twenty's GitHub repository\"],\"User guide\":[\"User guide\"],\"Read Twenty's user guide\":[\"Read Twenty's user guide\"],\"Developers\":[\"Developers\"],\"Visit Twenty's developer documentation\":[\"Visit Twenty's developer documentation\"],\"Twenty.com, Public Benefit Corporation\":[\"Twenty.com, Public Benefit Corporation\"],\"San Francisco / Paris\":[\"San Francisco / Paris\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\":[\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\"],\"4WPI3S\":[\"Accept invite\"],\"Yxj+Uc\":[\"All data in this workspace has been permanently deleted.\"],\"RPHFhC\":[\"Confirm your email address\"],\"nvkBPN\":[\"Connect to Twenty\"],\"jPQSEz\":[\"Create a new workspace\"],\"JRzgV7\":[\"Dear\"],\"Lm5BBI\":[\"Dear \",[\"userName\"]],\"lIdkf2\":[\"Dear \",[\"userName\"],\",\"],\"NTwcnq\":[\"Deleted Workspace\"],\"S3uuQj\":[\"email addresses to join your workspace without requiring an invitation.\"],\"tGme7M\":[\"has invited you to join a workspace called \"],\"uzTaYi\":[\"Hello\"],\"Xa0d85\":[\"Hello,\"],\"eE1nG1\":[\"If you did not initiate this change, please contact your workspace owner immediately.\"],\"Gz91L8\":[\"If you wish to continue using Twenty, please update your subscription within the next \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"0weyko\":[\"If you wish to use Twenty again, you can create a new workspace.\"],\"7JuhZQ\":[\"It appears that your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been suspended for \",[\"daysSinceInactive\"],\" days.\"],\"PviVyk\":[\"Join your team on Twenty\"],\"ogtYkT\":[\"Password updated\"],\"Yucjaa\":[\"Please validate this domain to allow users with\"],\"u3Ns4p\":[\"Please validate this domain to allow users with <0>@\",[\"domain\"],\"</0> email addresses to join your workspace without requiring an invitation.\"],\"OfhWJH\":[\"Reset\"],\"RE5NiU\":[\"Reset your password 🗝\"],\"UBadaJ\":[\"Suspended Workspace \"],\"7yDt8q\":[\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\"],\"igorB1\":[\"The workspace will be deactivated in \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\", and all its data will be deleted.\"],\"7OEHy1\":[\"This is a confirmation that password for your account (\",[\"email\"],\") was successfully changed on \",[\"formattedDate\"],\".\"],\"wSOsS+\":[\"This is a confirmation that password for your account (\",[\"email\"],\") was successfully changed on \",[\"formattedDate\"],\".<0/><1/>If you did not initiate this change, please contact your workspace owner immediately.\"],\"R4gMjN\":[\"This link is only valid for the next \",[\"duration\"],\". If the link does not work, you can use the login verification link directly:\"],\"2oA637\":[\"This link is only valid for the next \",[\"duration\"],\". If the link does not work, you can use the login verification link directly:<0/>\"],\"H0v4yC\":[\"Update your subscription\"],\"QbiUqd\":[\"Validate domain\"],\"wCKkSr\":[\"Verify Email\"],\"9MqLGX\":[\"Your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been deleted as your subscription expired \",[\"daysSinceInactive\"],\" days ago.\"],\"KFmFrQ\":[\"Your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been deleted as your subscription expired \",[\"inactiveDaysBeforeDelete\"],\" days ago.\"]}")as Messages;