/*eslint-disable*/import type{Messages}from"@lingui/core";export const messages=JSON.parse("{\"Suspended Workspace\":[\"Suspended Workspace\"],\"Dear {userName},\":[\"Dear \",[\"userName\"],\",\"],\"Hello,\":[\"Hello,\"],\"It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days.\":[\"It appears that your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been suspended for \",[\"daysSinceInactive\"],\" days.\"],\"The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted.\":[\"The workspace will be deactivated in \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\", and all its data will be deleted.\"],\"If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}.\":[\"If you wish to continue using Twenty, please update your subscription within the next \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"Update your subscription\":[\"Update your subscription\"],\"Validate domain\":[\"Validate domain\"],\"{senderName} (<0>{senderEmail}</0>): Please validate this domain to allow users with <1>@{domain}</1> email addresses to join your workspace without requiring an invitation.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>): Please validate this domain to allow users with <1>@\",[\"domain\"],\"</1> email addresses to join your workspace without requiring an invitation.\"],\"Test email\":[\"Test email\"],\"Join your team on Twenty\":[\"Join your team on Twenty\"],\"{senderName} (<0>{senderEmail}</0>) has invited you to join a workspace called <1>{workspaceName}</1>.\":[[\"senderName\"],\" (<0>\",[\"senderEmail\"],\"</0>) has invited you to join a workspace called <1>\",[\"workspaceName\"],\"</1>.\"],\"Accept invite\":[\"Accept invite\"],\"Confirm your email address\":[\"Confirm your email address\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address.\":[\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address.\"],\"Verify Email\":[\"Verify Email\"],\"Password updated\":[\"Password updated\"],\"This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.\":[\"This is a confirmation that password for your account (\",[\"email\"],\") was successfully changed on \",[\"formattedDate\"],\".\"],\"If you did not initiate this change, please contact your workspace owner immediately.\":[\"If you did not initiate this change, please contact your workspace owner immediately.\"],\"Connect to Twenty\":[\"Connect to Twenty\"],\"Reset your password 🗝\":[\"Reset your password 🗝\"],\"This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:\":[\"This link is only valid for the next \",[\"duration\"],\". If the link does not work, you can use the login verification link directly:\"],\"Reset\":[\"Reset\"],\"Deleted Workspace\":[\"Deleted Workspace\"],\"Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago.\":[\"Your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been deleted as your subscription expired \",[\"daysSinceInactive\"],\" days ago.\"],\"All data in this workspace has been permanently deleted.\":[\"All data in this workspace has been permanently deleted.\"],\"If you wish to use Twenty again, you can create a new workspace.\":[\"If you wish to use Twenty again, you can create a new workspace.\"],\"Create a new workspace\":[\"Create a new workspace\"],\"What is Twenty?\":[\"What is Twenty?\"],\"It's a CRM, a software to help businesses manage their customer data and relationships efficiently.\":[\"It's a CRM, a software to help businesses manage their customer data and relationships efficiently.\"],\"Website\":[\"Website\"],\"Visit Twenty's website\":[\"Visit Twenty's website\"],\"Github\":[\"Github\"],\"Visit Twenty's GitHub repository\":[\"Visit Twenty's GitHub repository\"],\"User guide\":[\"User guide\"],\"Read Twenty's user guide\":[\"Read Twenty's user guide\"],\"Developers\":[\"Developers\"],\"Visit Twenty's developer documentation\":[\"Visit Twenty's developer documentation\"],\"Twenty.com, Public Benefit Corporation\":[\"Twenty.com, Public Benefit Corporation\"],\"San Francisco / Paris\":[\"San Francisco / Paris\"],\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\":[\"Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address.\"],\"4WPI3S\":[\"Accepter l'invitation\"],\"Yxj+Uc\":[\"Toutes les données de cet espace de travail ont été supprimées définitivement.\"],\"RPHFhC\":[\"Confirmez votre adresse e-mail\"],\"nvkBPN\":[\"Connectez-vous à Twenty\"],\"jPQSEz\":[\"Créez un nouvel espace de travail\"],\"JRzgV7\":[\"Dear\"],\"Lm5BBI\":[\"Cher \",[\"userName\"]],\"lIdkf2\":[\"Cher \",[\"userName\"],\",\"],\"NTwcnq\":[\"Espace de travail supprimé\"],\"S3uuQj\":[\"adresses e-mail pour rejoindre votre espace de travail sans nécessiter d'invitation.\"],\"tGme7M\":[\"vous a invité à rejoindre un espace de travail appelé \"],\"uzTaYi\":[\"Bonjour\"],\"Xa0d85\":[\"Bonjour,\"],\"eE1nG1\":[\"Si vous n'avez pas initié ce changement, veuillez contacter immédiatement le propriétaire de votre espace de travail.\"],\"Gz91L8\":[\"Pour continuer à utiliser Twenty, veuillez mettre à jour votre abonnement dans les \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\".\"],\"0weyko\":[\"Pour réutiliser Twenty, vous pouvez créer un nouvel espace de travail.\"],\"7JuhZQ\":[\"Il semble que votre espace de travail <0>\",[\"workspaceDisplayName\"],\"</0> soit suspendu depuis \",[\"daysSinceInactive\"],\" jours.\"],\"PviVyk\":[\"Rejoignez votre équipe sur Twenty\"],\"ogtYkT\":[\"Mot de passe mis à jour\"],\"Yucjaa\":[\"Veuillez valider ce domaine pour permettre aux utilisateurs avec\"],\"u3Ns4p\":[\"Please validate this domain to allow users with <0>@\",[\"domain\"],\"</0> email addresses to join your workspace without requiring an invitation.\"],\"OfhWJH\":[\"Réinitialiser\"],\"RE5NiU\":[\"Réinitialisez votre mot de passe 🗝\"],\"UBadaJ\":[\"Espace de travail suspendu \"],\"7yDt8q\":[\"Merci de vous être inscrit sur Twenty ! Avant de commencer, nous devons confirmer votre identité. Cliquez ci-dessus pour vérifier votre adresse e-mail.\"],\"igorB1\":[\"L'espace de travail sera désactivé dans \",[\"remainingDays\"],\" \",[\"dayOrDays\"],\" et toutes ses données seront supprimées.\"],\"7OEHy1\":[\"Ceci est une confirmation que le mot de passe de votre compte (\",[\"email\"],\") a été modifié avec succès le \",[\"formattedDate\"],\".\"],\"wSOsS+\":[\"This is a confirmation that password for your account (\",[\"email\"],\") was successfully changed on \",[\"formattedDate\"],\".<0/><1/>If you did not initiate this change, please contact your workspace owner immediately.\"],\"R4gMjN\":[\"Ce lien n'est valable que pour les \",[\"duration\"],\" suivants. Si le lien ne fonctionne pas, vous pouvez utiliser directement le lien de vérification de connexion :\"],\"2oA637\":[\"This link is only valid for the next \",[\"duration\"],\". If the link does not work, you can use the login verification link directly:<0/>\"],\"H0v4yC\":[\"Mettez à jour votre abonnement\"],\"QbiUqd\":[\"Valider le domaine\"],\"wCKkSr\":[\"Vérifiez l'e-mail\"],\"9MqLGX\":[\"Votre espace de travail <0>\",[\"workspaceDisplayName\"],\"</0> a été supprimé car votre abonnement a expiré il y a \",[\"daysSinceInactive\"],\" jours.\"],\"KFmFrQ\":[\"Your workspace <0>\",[\"workspaceDisplayName\"],\"</0> has been deleted as your subscription expired \",[\"inactiveDaysBeforeDelete\"],\" days ago.\"]}")as Messages;