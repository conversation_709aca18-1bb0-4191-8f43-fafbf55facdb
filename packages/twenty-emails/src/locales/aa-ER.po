msgid ""
msgstr ""
"POT-Creation-Date: 2025-02-01 18:53+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: aa\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Afar\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: aa\n"
"X-Crowdin-File: /packages/twenty-emails/src/locales/en.po\n"
"X-Crowdin-File-ID: 27\n"

#. js-lingui-id: 4WPI3S
#: src/emails/send-invite-link.email.tsx
msgid "Accept invite"
msgstr "crwdns1:0crwdne1:0"

#. js-lingui-id: Yxj+Uc
#: src/emails/clean-suspended-workspace.email.tsx
msgid "All data in this workspace has been permanently deleted."
msgstr "crwdns3:0crwdne3:0"

#. js-lingui-id: RPHFhC
#: src/emails/send-email-verification-link.email.tsx
msgid "Confirm your email address"
msgstr "crwdns5:0crwdne5:0"

#. js-lingui-id: nvkBPN
#: src/emails/password-update-notify.email.tsx
msgid "Connect to Twenty"
msgstr "crwdns7:0crwdne7:0"

#. js-lingui-id: jPQSEz
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Create a new workspace"
msgstr "crwdns4865:0crwdne4865:0"

#. js-lingui-id: JRzgV7
#: src/emails/password-update-notify.email.tsx
#~ msgid "Dear"
#~ msgstr "Dear"

#. js-lingui-id: Lm5BBI
#: src/emails/password-update-notify.email.tsx
msgid "Dear {userName}"
msgstr "crwdns9:0{userName}crwdne9:0"

#. js-lingui-id: lIdkf2
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Dear {userName},"
msgstr "crwdns4867:0{userName}crwdne4867:0"

#. js-lingui-id: NTwcnq
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Deleted Workspace"
msgstr "crwdns4869:0crwdne4869:0"

#. js-lingui-id: S3uuQj
#: src/emails/validate-approved-access-domain.email.tsx
msgid "email addresses to join your workspace without requiring an invitation."
msgstr "crwdns5423:0crwdne5423:0"

#. js-lingui-id: tGme7M
#: src/emails/send-invite-link.email.tsx
msgid "has invited you to join a workspace called "
msgstr "crwdns11:0crwdne11:0"

#. js-lingui-id: uzTaYi
#: src/emails/password-update-notify.email.tsx
msgid "Hello"
msgstr "crwdns13:0crwdne13:0"

#. js-lingui-id: Xa0d85
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Hello,"
msgstr "crwdns4871:0crwdne4871:0"

#. js-lingui-id: eE1nG1
#: src/emails/password-update-notify.email.tsx
msgid "If you did not initiate this change, please contact your workspace owner immediately."
msgstr "crwdns15:0crwdne15:0"

#. js-lingui-id: Gz91L8
#: src/emails/warn-suspended-workspace.email.tsx
msgid "If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}."
msgstr "crwdns17:0{remainingDays}crwdnd17:0{dayOrDays}crwdne17:0"

#. js-lingui-id: 0weyko
#: src/emails/clean-suspended-workspace.email.tsx
msgid "If you wish to use Twenty again, you can create a new workspace."
msgstr "crwdns19:0crwdne19:0"

#. js-lingui-id: 7JuhZQ
#: src/emails/warn-suspended-workspace.email.tsx
msgid "It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days."
msgstr "crwdns21:0{workspaceDisplayName}crwdnd21:0{daysSinceInactive}crwdne21:0"

#. js-lingui-id: PviVyk
#: src/emails/send-invite-link.email.tsx
msgid "Join your team on Twenty"
msgstr "crwdns23:0crwdne23:0"

#. js-lingui-id: ogtYkT
#: src/emails/password-update-notify.email.tsx
msgid "Password updated"
msgstr "crwdns25:0crwdne25:0"

#. js-lingui-id: Yucjaa
#: src/emails/validate-approved-access-domain.email.tsx
msgid "Please validate this domain to allow users with"
msgstr "crwdns5425:0crwdne5425:0"

#. js-lingui-id: u3Ns4p
#: src/emails/validate-approved-access-domain.email.tsx
#~ msgid "Please validate this domain to allow users with <0>@{domain}</0> email addresses to join your workspace without requiring an invitation."
#~ msgstr "Please validate this domain to allow users with <0>@{domain}</0> email addresses to join your workspace without requiring an invitation."

#. js-lingui-id: OfhWJH
#: src/emails/password-reset-link.email.tsx
msgid "Reset"
msgstr "crwdns27:0crwdne27:0"

#. js-lingui-id: RE5NiU
#: src/emails/password-reset-link.email.tsx
msgid "Reset your password 🗝"
msgstr "crwdns29:0crwdne29:0"

#. js-lingui-id: UBadaJ
#: src/emails/warn-suspended-workspace.email.tsx
msgid "Suspended Workspace "
msgstr "crwdns4875:0crwdne4875:0"

#. js-lingui-id: 7yDt8q
#: src/emails/send-email-verification-link.email.tsx
msgid "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address."
msgstr "crwdns31:0crwdne31:0"

#. js-lingui-id: igorB1
#: src/emails/warn-suspended-workspace.email.tsx
msgid "The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted."
msgstr "crwdns33:0{remainingDays}crwdnd33:0{dayOrDays}crwdne33:0"

#. js-lingui-id: 7OEHy1
#: src/emails/password-update-notify.email.tsx
msgid "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}."
msgstr "crwdns35:0{email}crwdnd35:0{formattedDate}crwdne35:0"

#. js-lingui-id: wSOsS+
#: src/emails/password-update-notify.email.tsx
#~ msgid "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.<0/><1/>If you did not initiate this change, please contact your workspace owner immediately."
#~ msgstr "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.<0/><1/>If you did not initiate this change, please contact your workspace owner immediately."

#. js-lingui-id: R4gMjN
#: src/emails/password-reset-link.email.tsx
msgid "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:"
msgstr "crwdns37:0{duration}crwdne37:0"

#. js-lingui-id: 2oA637
#: src/emails/password-reset-link.email.tsx
#~ msgid "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:<0/>"
#~ msgstr "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:<0/>"

#. js-lingui-id: H0v4yC
#: src/emails/warn-suspended-workspace.email.tsx
msgid "Update your subscription"
msgstr "crwdns39:0crwdne39:0"

#. js-lingui-id: QbiUqd
#: src/emails/validate-approved-access-domain.email.tsx
#: src/emails/validate-approved-access-domain.email.tsx
msgid "Validate domain"
msgstr "crwdns4877:0crwdne4877:0"

#. js-lingui-id: wCKkSr
#: src/emails/send-email-verification-link.email.tsx
msgid "Verify Email"
msgstr "crwdns41:0crwdne41:0"

#. js-lingui-id: 9MqLGX
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago."
msgstr "crwdns4845:0{workspaceDisplayName}crwdnd4845:0{daysSinceInactive}crwdne4845:0"

#. js-lingui-id: KFmFrQ
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {inactiveDaysBeforeDelete} days ago."
#~ msgstr "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {inactiveDaysBeforeDelete} days ago."
