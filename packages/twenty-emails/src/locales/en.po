msgid ""
msgstr ""
"POT-Creation-Date: 2025-02-01 18:53+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
msgid "Suspended Workspace"
msgstr "Suspended Workspace"

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
#: src/emails/password-update-notify.email.tsx
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Dear {userName},"
msgstr "Dear {userName},"

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
#: src/emails/password-update-notify.email.tsx
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Hello,"
msgstr "Hello,"

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
msgid "It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days."
msgstr "It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days."

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
msgid "The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted."
msgstr "The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted."

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
msgid "If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}."
msgstr "If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}."

#. js-lingui-explicit-id
#: src/emails/warn-suspended-workspace.email.tsx
msgid "Update your subscription"
msgstr "Update your subscription"

#. js-lingui-explicit-id
#: src/emails/validate-approved-access-domain.email.tsx
#: src/emails/validate-approved-access-domain.email.tsx
msgid "Validate domain"
msgstr "Validate domain"

#. js-lingui-explicit-id
#: src/emails/validate-approved-access-domain.email.tsx
msgid "{senderName} (<0>{senderEmail}</0>): Please validate this domain to allow users with <1>@{domain}</1> email addresses to join your workspace without requiring an invitation."
msgstr "{senderName} (<0>{senderEmail}</0>): Please validate this domain to allow users with <1>@{domain}</1> email addresses to join your workspace without requiring an invitation."

#. js-lingui-explicit-id
#: src/emails/test.email.tsx
msgid "Test email"
msgstr "Test email"

#. js-lingui-explicit-id
#: src/emails/send-invite-link.email.tsx
msgid "Join your team on Twenty"
msgstr "Join your team on Twenty"

#. js-lingui-explicit-id
#: src/emails/send-invite-link.email.tsx
msgid "{senderName} (<0>{senderEmail}</0>) has invited you to join a workspace called <1>{workspaceName}</1>."
msgstr "{senderName} (<0>{senderEmail}</0>) has invited you to join a workspace called <1>{workspaceName}</1>."

#. js-lingui-explicit-id
#: src/emails/send-invite-link.email.tsx
msgid "Accept invite"
msgstr "Accept invite"

#. js-lingui-explicit-id
#: src/emails/send-email-verification-link.email.tsx
msgid "Confirm your email address"
msgstr "Confirm your email address"

#. js-lingui-explicit-id
#: src/emails/send-email-verification-link.email.tsx
msgid "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address."
msgstr "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click below to verify your email address."

#. js-lingui-explicit-id
#: src/emails/send-email-verification-link.email.tsx
msgid "Verify Email"
msgstr "Verify Email"

#. js-lingui-explicit-id
#: src/emails/password-update-notify.email.tsx
msgid "Password updated"
msgstr "Password updated"

#. js-lingui-explicit-id
#: src/emails/password-update-notify.email.tsx
msgid "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}."
msgstr "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}."

#. js-lingui-explicit-id
#: src/emails/password-update-notify.email.tsx
msgid "If you did not initiate this change, please contact your workspace owner immediately."
msgstr "If you did not initiate this change, please contact your workspace owner immediately."

#. js-lingui-explicit-id
#: src/emails/password-update-notify.email.tsx
msgid "Connect to Twenty"
msgstr "Connect to Twenty"

#. js-lingui-explicit-id
#: src/emails/password-reset-link.email.tsx
msgid "Reset your password 🗝"
msgstr "Reset your password 🗝"

#. js-lingui-explicit-id
#: src/emails/password-reset-link.email.tsx
msgid "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:"
msgstr "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:"

#. js-lingui-explicit-id
#: src/emails/password-reset-link.email.tsx
msgid "Reset"
msgstr "Reset"

#. js-lingui-explicit-id
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Deleted Workspace"
msgstr "Deleted Workspace"

#. js-lingui-explicit-id
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago."
msgstr "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago."

#. js-lingui-explicit-id
#: src/emails/clean-suspended-workspace.email.tsx
msgid "All data in this workspace has been permanently deleted."
msgstr "All data in this workspace has been permanently deleted."

#. js-lingui-explicit-id
#: src/emails/clean-suspended-workspace.email.tsx
msgid "If you wish to use Twenty again, you can create a new workspace."
msgstr "If you wish to use Twenty again, you can create a new workspace."

#. js-lingui-explicit-id
#: src/emails/clean-suspended-workspace.email.tsx
msgid "Create a new workspace"
msgstr "Create a new workspace"

#. js-lingui-explicit-id
#: src/components/WhatIsTwenty.tsx
msgid "What is Twenty?"
msgstr "What is Twenty?"

#. js-lingui-explicit-id
#: src/components/WhatIsTwenty.tsx
msgid "It's a CRM, a software to help businesses manage their customer data and relationships efficiently."
msgstr "It's a CRM, a software to help businesses manage their customer data and relationships efficiently."

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Website"
msgstr "Website"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Visit Twenty's website"
msgstr "Visit Twenty's website"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Github"
msgstr "Github"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Visit Twenty's GitHub repository"
msgstr "Visit Twenty's GitHub repository"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "User guide"
msgstr "User guide"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Read Twenty's user guide"
msgstr "Read Twenty's user guide"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Developers"
msgstr "Developers"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Visit Twenty's developer documentation"
msgstr "Visit Twenty's developer documentation"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "Twenty.com, Public Benefit Corporation"
msgstr "Twenty.com, Public Benefit Corporation"

#. js-lingui-explicit-id
#: src/components/Footer.tsx
msgid "San Francisco / Paris"
msgstr "San Francisco / Paris"

#. js-lingui-explicit-id
#: src/emails/send-email-verification-link.email.tsx
#~ msgid "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address."
#~ msgstr "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address."

#. js-lingui-id: 4WPI3S
#: src/emails/send-invite-link.email.tsx
#~ msgid "Accept invite"
#~ msgstr "Accept invite"

#. js-lingui-id: Yxj+Uc
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "All data in this workspace has been permanently deleted."
#~ msgstr "All data in this workspace has been permanently deleted."

#. js-lingui-id: RPHFhC
#: src/emails/send-email-verification-link.email.tsx
#~ msgid "Confirm your email address"
#~ msgstr "Confirm your email address"

#. js-lingui-id: nvkBPN
#: src/emails/password-update-notify.email.tsx
#~ msgid "Connect to Twenty"
#~ msgstr "Connect to Twenty"

#. js-lingui-id: jPQSEz
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Create a new workspace"
#~ msgstr "Create a new workspace"

#. js-lingui-id: JRzgV7
#: src/emails/password-update-notify.email.tsx
#~ msgid "Dear"
#~ msgstr "Dear"

#. js-lingui-id: Lm5BBI
#: src/emails/password-update-notify.email.tsx
#~ msgid "Dear {userName}"
#~ msgstr "Dear {userName}"

#. js-lingui-id: lIdkf2
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Dear {userName},"
#~ msgstr "Dear {userName},"

#. js-lingui-id: NTwcnq
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Deleted Workspace"
#~ msgstr "Deleted Workspace"

#. js-lingui-id: S3uuQj
#: src/emails/validate-approved-access-domain.email.tsx
#~ msgid "email addresses to join your workspace without requiring an invitation."
#~ msgstr "email addresses to join your workspace without requiring an invitation."

#. js-lingui-id: tGme7M
#: src/emails/send-invite-link.email.tsx
#~ msgid "has invited you to join a workspace called "
#~ msgstr "has invited you to join a workspace called "

#. js-lingui-id: uzTaYi
#: src/emails/password-update-notify.email.tsx
#~ msgid "Hello"
#~ msgstr "Hello"

#. js-lingui-id: Xa0d85
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Hello,"
#~ msgstr "Hello,"

#. js-lingui-id: eE1nG1
#: src/emails/password-update-notify.email.tsx
#~ msgid "If you did not initiate this change, please contact your workspace owner immediately."
#~ msgstr "If you did not initiate this change, please contact your workspace owner immediately."

#. js-lingui-id: Gz91L8
#: src/emails/warn-suspended-workspace.email.tsx
#~ msgid "If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}."
#~ msgstr "If you wish to continue using Twenty, please update your subscription within the next {remainingDays} {dayOrDays}."

#. js-lingui-id: 0weyko
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "If you wish to use Twenty again, you can create a new workspace."
#~ msgstr "If you wish to use Twenty again, you can create a new workspace."

#. js-lingui-id: 7JuhZQ
#: src/emails/warn-suspended-workspace.email.tsx
#~ msgid "It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days."
#~ msgstr "It appears that your workspace <0>{workspaceDisplayName}</0> has been suspended for {daysSinceInactive} days."

#. js-lingui-id: PviVyk
#: src/emails/send-invite-link.email.tsx
#~ msgid "Join your team on Twenty"
#~ msgstr "Join your team on Twenty"

#. js-lingui-id: ogtYkT
#: src/emails/password-update-notify.email.tsx
#~ msgid "Password updated"
#~ msgstr "Password updated"

#. js-lingui-id: Yucjaa
#: src/emails/validate-approved-access-domain.email.tsx
#~ msgid "Please validate this domain to allow users with"
#~ msgstr "Please validate this domain to allow users with"

#. js-lingui-id: u3Ns4p
#: src/emails/validate-approved-access-domain.email.tsx
#~ msgid "Please validate this domain to allow users with <0>@{domain}</0> email addresses to join your workspace without requiring an invitation."
#~ msgstr "Please validate this domain to allow users with <0>@{domain}</0> email addresses to join your workspace without requiring an invitation."

#. js-lingui-id: OfhWJH
#: src/emails/password-reset-link.email.tsx
#~ msgid "Reset"
#~ msgstr "Reset"

#. js-lingui-id: RE5NiU
#: src/emails/password-reset-link.email.tsx
#~ msgid "Reset your password 🗝"
#~ msgstr "Reset your password 🗝"

#. js-lingui-id: UBadaJ
#: src/emails/warn-suspended-workspace.email.tsx
#~ msgid "Suspended Workspace "
#~ msgstr "Suspended Workspace "

#. js-lingui-id: 7yDt8q
#: src/emails/send-email-verification-link.email.tsx
#~ msgid "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address."
#~ msgstr "Thanks for registering for an account on Twenty! Before we get started, we just need to confirm that this is you. Click above to verify your email address."

#. js-lingui-id: igorB1
#: src/emails/warn-suspended-workspace.email.tsx
#~ msgid "The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted."
#~ msgstr "The workspace will be deactivated in {remainingDays} {dayOrDays}, and all its data will be deleted."

#. js-lingui-id: 7OEHy1
#: src/emails/password-update-notify.email.tsx
#~ msgid "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}."
#~ msgstr "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}."

#. js-lingui-id: wSOsS+
#: src/emails/password-update-notify.email.tsx
#~ msgid "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.<0/><1/>If you did not initiate this change, please contact your workspace owner immediately."
#~ msgstr "This is a confirmation that password for your account ({email}) was successfully changed on {formattedDate}.<0/><1/>If you did not initiate this change, please contact your workspace owner immediately."

#. js-lingui-id: R4gMjN
#: src/emails/password-reset-link.email.tsx
#~ msgid "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:"
#~ msgstr "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:"

#. js-lingui-id: 2oA637
#: src/emails/password-reset-link.email.tsx
#~ msgid "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:<0/>"
#~ msgstr "This link is only valid for the next {duration}. If the link does not work, you can use the login verification link directly:<0/>"

#. js-lingui-id: H0v4yC
#: src/emails/warn-suspended-workspace.email.tsx
#~ msgid "Update your subscription"
#~ msgstr "Update your subscription"

#. js-lingui-id: QbiUqd
#: src/emails/validate-approved-access-domain.email.tsx
#: src/emails/validate-approved-access-domain.email.tsx
#~ msgid "Validate domain"
#~ msgstr "Validate domain"

#. js-lingui-id: wCKkSr
#: src/emails/send-email-verification-link.email.tsx
#~ msgid "Verify Email"
#~ msgstr "Verify Email"

#. js-lingui-id: 9MqLGX
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago."
#~ msgstr "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {daysSinceInactive} days ago."

#. js-lingui-id: KFmFrQ
#: src/emails/clean-suspended-workspace.email.tsx
#~ msgid "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {inactiveDaysBeforeDelete} days ago."
#~ msgstr "Your workspace <0>{workspaceDisplayName}</0> has been deleted as your subscription expired {inactiveDaysBeforeDelete} days ago."
