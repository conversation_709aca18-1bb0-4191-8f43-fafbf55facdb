msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-30 18:16+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: aa\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Afar\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: aa\n"
"X-Crowdin-File: /packages/twenty-front/src/locales/en.po\n"
"X-Crowdin-File-ID: 29\n"

#. js-lingui-id: gdf0h7
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
#~ msgid ""
#~ " These are only the server values. Ensure your worker environment has the\n"
#~ "        same variables and values, this is required for asynchronous tasks like\n"
#~ "        email sync."
#~ msgstr ""
#~ " These are only the server values. Ensure your worker environment has the\n"
#~ "        same variables and values, this is required for asynchronous tasks like\n"
#~ "        email sync."

#. js-lingui-id: ypz2+E
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Empty"
msgstr "crwdns4985:0crwdne4985:0"

#. js-lingui-id: CE75IR
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Future"
msgstr "crwdns4987:0crwdne4987:0"

#. js-lingui-id: Nk/d+Z
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Not"
msgstr "crwdns4989:0crwdne4989:0"

#. js-lingui-id: 1ORnec
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": NotEmpty"
msgstr "crwdns4991:0crwdne4991:0"

#. js-lingui-id: 11QpPG
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": NotNull"
msgstr "crwdns4993:0crwdne4993:0"

#. js-lingui-id: gK4ysT
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Past"
msgstr "crwdns4995:0crwdne4995:0"

#. js-lingui-id: 4iJDkt
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Today"
msgstr "crwdns4997:0crwdne4997:0"

#. js-lingui-id: ZBGbuw
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
msgid "[empty string]"
msgstr "crwdns5584:0crwdne5584:0"

#. js-lingui-id: J/hVSQ
#. placeholder {0}: theme.id
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownThemesComponents.tsx
msgid "{0}"
msgstr "crwdns5561:0{0}crwdne5561:0"

#. js-lingui-id: ROdDR9
#: src/modules/object-record/record-board/record-board-column/utils/computeAggregateValueAndLabel.ts
msgid "{aggregateLabel} of {fieldLabel}"
msgstr "crwdns45:0{aggregateLabel}crwdnd45:0{fieldLabel}crwdne45:0"

#. js-lingui-id: uogEAL
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "{apiKeyName} API Key"
msgstr "crwdns47:0{apiKeyName}crwdne47:0"

#. js-lingui-id: zi5SHH
#: src/modules/object-record/record-index/components/RecordIndexPageHeader.tsx
msgid "{contextStoreNumberOfSelectedRecords} selected"
msgstr "crwdns5630:0{contextStoreNumberOfSelectedRecords}crwdne5630:0"

#. js-lingui-id: 6j5rE1
#: src/modules/action-menu/actions/record-agnostic-actions/run-workflow-actions/hooks/useRunWorkflowActions.tsx
#: src/modules/action-menu/actions/record-actions/workflow-run-record-actions/hooks/useWorkflowRunRecordActions.tsx
msgid "{name}"
msgstr "crwdns49:0{name}crwdne49:0"

#. js-lingui-id: WN9tFl
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentConfirmationModalSubtitle.tsx
msgid "{workspaceMemberName} will be unassigned from the following role:"
msgstr "crwdns1783:0{workspaceMemberName}crwdne1783:0"

#. js-lingui-id: YT0WJ4
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "1 000 workflow node executions"
#~ msgstr "1 000 workflow node executions"

#. js-lingui-id: vb5TwV
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
msgid "1. Select a field type"
msgstr "crwdns4999:0crwdne4999:0"

#. js-lingui-id: SLjiTq
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
msgid "1. Type"
msgstr "crwdns5001:0crwdne5001:0"

#. js-lingui-id: AvXug3
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "10 000 workflow node executions"
msgstr "crwdns5590:0crwdne5590:0"

#. js-lingui-id: 4EdXYs
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "12h ({hour12Label})"
msgstr "crwdns53:0{hour12Label}crwdne53:0"

#. js-lingui-id: yXvRMf
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
msgid "2. Configure"
msgstr "crwdns5003:0crwdne5003:0"

#. js-lingui-id: 0HAF12
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "2. Configure field"
msgstr "crwdns55:0crwdne55:0"

#. js-lingui-id: kAtmAv
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "20 000 workflow node executions"
msgstr "crwdns5592:0crwdne5592:0"

#. js-lingui-id: QsMprd
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "24h ({hour24Label})"
msgstr "crwdns57:0{hour24Label}crwdne57:0"

#. js-lingui-id: nMTB1f
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "A shared environment where you will be able to manage your customer relations with your team."
msgstr "crwdns59:0crwdne59:0"

#. js-lingui-id: 09tRFp
#: src/modules/settings/roles/role-permissions/components/RolePermissions.tsx
msgid "Ability to interact with each object"
msgstr "crwdns4837:0crwdne4837:0"

#. js-lingui-id: ssjjFt
#: src/modules/ui/input/components/ImageInput.tsx
msgid "Abort"
msgstr "crwdns61:0crwdne61:0"

#. js-lingui-id: uyJsf6
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "About"
msgstr "crwdns63:0crwdne63:0"

#. js-lingui-id: DhgC7B
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "About this user"
msgstr "crwdns5525:0crwdne5525:0"

#. js-lingui-id: PtK3Kn
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "About this workspace"
msgstr "crwdns5527:0crwdne5527:0"

#. js-lingui-id: AeXO77
#: src/pages/settings/accounts/SettingsAccounts.tsx
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Account"
msgstr "crwdns65:0crwdne65:0"

#. js-lingui-id: nD0Y+a
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
msgid "Account Deletion"
msgstr "crwdns67:0crwdne67:0"

#. js-lingui-id: bPwFdf
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Accounts"
msgstr "crwdns69:0crwdne69:0"

#. js-lingui-id: bwRvnp
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "Action"
msgstr "crwdns5594:0crwdne5594:0"

#. js-lingui-id: 7L01XJ
#: src/modules/settings/roles/role-permissions/components/RolePermissionsSettingsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissionsObjectsTableHeader.tsx
msgid "Actions"
msgstr "crwdns1785:0crwdne1785:0"

#. js-lingui-id: FQBaXG
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Activate"
msgstr "crwdns71:0crwdne71:0"

#. js-lingui-id: tu8A/k
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Activate Workflow"
msgstr "crwdns73:0crwdne73:0"

#. js-lingui-id: F6pfE9
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Active"
msgstr "crwdns75:0crwdne75:0"

#. js-lingui-id: Mue4oc
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Active API keys created by you or your team."
msgstr "crwdns77:0crwdne77:0"

#. js-lingui-id: m16xKo
#: src/modules/ui/layout/page/components/PageAddButton.tsx
#~ msgid "Add"
#~ msgstr "Add"

#. js-lingui-id: MPPZ54
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsListCard.tsx
msgid "Add account"
msgstr "crwdns5005:0crwdne5005:0"

#. js-lingui-id: HD0x5p
#: src/modules/settings/security/components/approvedAccessDomains/SettingsApprovedAccessDomainsListCard.tsx
msgid "Add Approved Access Domain"
msgstr "crwdns4879:0crwdne4879:0"

#. js-lingui-id: DpV70M
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormBuilder.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
msgid "Add Field"
msgstr "crwdns81:0crwdne81:0"

#. js-lingui-id: vCSBPD
#: src/modules/object-record/object-filter-dropdown/components/AddObjectFilterFromDetailsButton.tsx
msgid "Add filter"
msgstr "crwdns5007:0crwdne5007:0"

#. js-lingui-id: Hkobke
#: src/modules/object-record/record-table/record-table-section/components/RecordTableRecordGroupSectionAddNew.tsx
msgid "Add new"
msgstr "crwdns5009:0crwdne5009:0"

#. js-lingui-id: dEO3Zx
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Add object"
msgstr "crwdns83:0crwdne83:0"

#. js-lingui-id: Dl5lVI
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
msgid "Add option"
msgstr "crwdns5011:0crwdne5011:0"

#. js-lingui-id: sgXUv+
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersListCard.tsx
msgid "Add SSO Identity Provider"
msgstr "crwdns85:0crwdne85:0"

#. js-lingui-id: 5+ttxv
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Add to blocklist"
msgstr "crwdns87:0crwdne87:0"

#. js-lingui-id: yVOmgE
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
msgid "Add to Favorite"
msgstr "crwdns5013:0crwdne5013:0"

#. js-lingui-id: pBsoKL
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuDropdown.stories.tsx
#: src/modules/action-menu/components/__stories__/CommandMenuActionMenuDropdown.stories.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Add to favorites"
msgstr "crwdns89:0crwdne89:0"

#. js-lingui-id: q9e2Bs
#: src/modules/views/view-picker/components/ViewPickerListContent.tsx
msgid "Add view"
msgstr "crwdns5015:0crwdne5015:0"

#. js-lingui-id: m2qDV8
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "Add your first {objectLabel}"
msgstr "crwdns91:0{objectLabel}crwdne91:0"

#. js-lingui-id: vLO+NG
#: src/modules/ui/layout/show-page/components/ShowPageSummaryCard.tsx
msgid "Added {beautifiedCreatedAt}"
msgstr "crwdns93:0{beautifiedCreatedAt}crwdne93:0"

#. js-lingui-id: jEHeq+
#: src/modules/settings/security/components/approvedAccessDomains/SettingsApprovedAccessDomainsListCard.tsx
msgid "Added {beautifyPastDateRelative}"
msgstr "crwdns5365:0{beautifyPastDateRelative}crwdne5365:0"

#. js-lingui-id: 9iIYOy
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistTable.tsx
msgid "Added to blocklist"
msgstr "crwdns5017:0crwdne5017:0"

#. js-lingui-id: Eis4ey
#: src/modules/settings/roles/components/RolesDefaultRole.tsx
msgid "Adjust the role-related settings"
msgstr "crwdns5355:0crwdne5355:0"

#. js-lingui-id: g1in8j
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Admin Panel"
msgstr "crwdns5465:0crwdne5465:0"

#. js-lingui-id: tMFFwF
#: src/modules/object-record/object-filter-dropdown/components/AdvancedFilterButton.tsx
msgid "Advanced filter"
msgstr "crwdns3979:0crwdne3979:0"

#. js-lingui-id: WsKDNF
#: src/modules/navigation/components/AppNavigationDrawer.tsx
msgid "Advanced:"
msgstr "crwdns5547:0crwdne5547:0"

#. js-lingui-id: 1Cox/a
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Afrikaans"
msgstr "crwdns1787:0crwdne1787:0"

#. js-lingui-id: N40H+G
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "All"
msgstr "crwdns95:0crwdne95:0"

#. js-lingui-id: 3saA7W
#: src/modules/object-record/record-show/record-detail-section/components/RecordDetailRelationSection.tsx
msgid "All ({relationRecordsCount})"
msgstr "crwdns97:0{relationRecordsCount}crwdne97:0"

#. js-lingui-id: 9ljU00
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "All Actions"
msgstr "crwdns5019:0crwdne5019:0"

#. js-lingui-id: 7EZqN0
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "All emails and events linked to this account will be deleted"
msgstr "crwdns5021:0crwdne5021:0"

#. js-lingui-id: 623MHa
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "All lines"
msgstr "crwdns5023:0crwdne5023:0"

#. js-lingui-id: aFE/OW
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "All Objects"
msgstr "crwdns5025:0crwdne5025:0"

#. js-lingui-id: Hm90t3
#: src/modules/settings/roles/components/Roles.tsx
msgid "All roles"
msgstr "crwdns99:0crwdne99:0"

#. js-lingui-id: MLmnB2
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "All workspaces this user is a member of"
msgstr "crwdns5529:0crwdne5529:0"

#. js-lingui-id: GMx1K0
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow logins through Google's single sign-on functionality."
msgstr "crwdns101:0crwdne101:0"

#. js-lingui-id: dea+zy
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow logins through Microsoft's single sign-on functionality."
msgstr "crwdns103:0crwdne103:0"

#. js-lingui-id: wMg43c
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow the invitation of new users by sharing an invite link."
msgstr "crwdns105:0crwdne105:0"

#. js-lingui-id: vHeVg5
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow users to sign in with an email and password."
msgstr "crwdns107:0crwdne107:0"

#. js-lingui-id: LG4K0m
#: src/pages/auth/PasswordReset.tsx
msgid "An error occurred while updating password"
msgstr "crwdns109:0crwdne109:0"

#. js-lingui-id: mJ6m4C
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "An optional description"
msgstr "crwdns111:0crwdne111:0"

#. js-lingui-id: lxentK
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "An unexpected error occurred"
msgstr "crwdns5435:0crwdne5435:0"

#. js-lingui-id: HZFm5R
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "and"
msgstr "crwdns113:0crwdne113:0"

#. js-lingui-id: xJR+Wq
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Anyone with an email address at these domains is allowed to sign up for this workspace."
msgstr "crwdns4881:0crwdne4881:0"

#. js-lingui-id: OZtEcz
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "API"
msgstr "crwdns5437:0crwdne5437:0"

#. js-lingui-id: 0RqpZr
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "API & Webhooks"
msgstr "crwdns115:0crwdne115:0"

#. js-lingui-id: yRnk5W
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "API Key"
msgstr "crwdns117:0crwdne117:0"

#. js-lingui-id: r+NRG6
#: src/modules/settings/developers/components/ApiKeyInput.tsx
msgid "API Key copied to clipboard"
msgstr "crwdns4883:0crwdne4883:0"

#. js-lingui-id: 5h8ooz
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "API keys"
msgstr "crwdns119:0crwdne119:0"

#. js-lingui-id: kAtj+q
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "API Name"
msgstr "crwdns121:0crwdne121:0"

#. js-lingui-id: lwCAhN
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "API Name (Plural)"
msgstr "crwdns3981:0crwdne3981:0"

#. js-lingui-id: KclpRp
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "API Name (Singular)"
msgstr "crwdns3983:0crwdne3983:0"

#. js-lingui-id: Z3Brb2
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
msgid "API values"
msgstr "crwdns5027:0crwdne5027:0"

#. js-lingui-id: JR6nY7
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "APIs"
msgstr "crwdns5439:0crwdne5439:0"

#. js-lingui-id: aAIQg2
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Appearance"
msgstr "crwdns123:0crwdne123:0"

#. js-lingui-id: 9tggYj
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Approved Domains"
msgstr "crwdns5367:0crwdne5367:0"

#. js-lingui-id: 1844JP
#: src/pages/settings/security/SettingsSecurity.tsx
#~ msgid "Approved Email Domain"
#~ msgstr "Approved Email Domain"

#. js-lingui-id: 8HV3WN
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Arabic"
msgstr "crwdns1789:0crwdne1789:0"

#. js-lingui-id: 3iX0kh
#: src/pages/settings/SettingsBilling.tsx
msgid "Are you sure that you want to change your billing interval?"
msgstr "crwdns125:0crwdne125:0"

#. js-lingui-id: 3SRf5B
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModalSubtitle.tsx
#~ msgid "Are you sure you want to assign this role?"
#~ msgstr "Are you sure you want to assign this role?"

#. js-lingui-id: 8Y3Jbl
#: src/modules/action-menu/actions/record-actions/multiple-records/hooks/useDeleteMultipleRecordsAction.tsx
#~ msgid "Are you sure you want to delete these records? They can be recovered from the Command menu ({osControlSymbol} + K)."
#~ msgstr "Are you sure you want to delete these records? They can be recovered from the Command menu ({osControlSymbol} + K)."

#. js-lingui-id: 2BZAqa
#: src/modules/action-menu/actions/record-actions/multiple-records/hooks/useDeleteMultipleRecordsAction.tsx
msgid "Are you sure you want to delete these records? They can be recovered from the Command menu."
msgstr "crwdns5596:0crwdne5596:0"

#. js-lingui-id: Se0vJw
#: src/modules/action-menu/actions/record-actions/single-record/hooks/useDeleteSingleRecordAction.tsx
#~ msgid "Are you sure you want to delete this record? It can be recovered from the Command menu ({osControlSymbol} + K)."
#~ msgstr "Are you sure you want to delete this record? It can be recovered from the Command menu ({osControlSymbol} + K)."

#. js-lingui-id: yb2hF4
#: src/modules/action-menu/actions/record-actions/single-record/hooks/useDeleteSingleRecordAction.tsx
msgid "Are you sure you want to delete this record? It can be recovered from the Command menu."
msgstr "crwdns5598:0crwdne5598:0"

#. js-lingui-id: nYD/Cq
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
msgid "Ascending"
msgstr "crwdns127:0crwdne127:0"

#. js-lingui-id: 9ch9Mz
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentConfirmationModal.tsx
msgid "Assign {workspaceMemberName}?"
msgstr "crwdns1793:0{workspaceMemberName}crwdne1793:0"

#. js-lingui-id: rfYmIr
#: src/modules/settings/roles/components/Roles.tsx
msgid "Assign roles to specify each member's access permissions"
msgstr "crwdns129:0crwdne129:0"

#. js-lingui-id: 2y2quh
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "Assign to member"
msgstr "crwdns1795:0crwdne1795:0"

#. js-lingui-id: OItM/o
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "Assigned members"
msgstr "crwdns131:0crwdne131:0"

#. js-lingui-id: lxQ+5m
#: src/modules/settings/roles/components/RolesTableHeader.tsx
msgid "Assigned to"
msgstr "crwdns133:0crwdne133:0"

#. js-lingui-id: 0dtKl9
#: src/pages/settings/roles/SettingsRoleEdit.tsx
msgid "Assignment"
msgstr "crwdns135:0crwdne135:0"

#. js-lingui-id: H8QGSx
#: src/modules/auth/sign-in-up/components/SignInUpPasswordField.tsx
msgid "At least 8 characters long."
msgstr "crwdns137:0crwdne137:0"

#. js-lingui-id: Y7Dx6e
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "At least one authentication method must be enabled"
msgstr "crwdns139:0crwdne139:0"

#. js-lingui-id: P8fBlG
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Authentication"
msgstr "crwdns141:0crwdne141:0"

#. js-lingui-id: htuqBH
#: src/modules/auth/hooks/useVerifyLogin.ts
msgid "Authentication failed"
msgstr "crwdns4887:0crwdne4887:0"

#. js-lingui-id: yIVrHZ
#: src/pages/auth/Authorize.tsx
msgid "Authorize"
msgstr "crwdns143:0crwdne143:0"

#. js-lingui-id: wTBNbL
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Authorized URI"
msgstr "crwdns5029:0crwdne5029:0"

#. js-lingui-id: Ovw0c6
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Authorized URL copied to clipboard"
msgstr "crwdns5031:0crwdne5031:0"

#. js-lingui-id: 2zJkmL
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Auto-creation"
msgstr "crwdns5033:0crwdne5033:0"

#. js-lingui-id: YRT7ZW
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Automatically create contacts for people you've participated in an event with."
msgstr "crwdns5035:0crwdne5035:0"

#. js-lingui-id: lgw3U4
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Automatically create contacts for people."
msgstr "crwdns5037:0crwdne5037:0"

#. js-lingui-id: RpExX0
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Automatically create People records when receiving or sending emails"
msgstr "crwdns5039:0crwdne5039:0"

#. js-lingui-id: 3uQmjD
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Average"
msgstr "crwdns145:0crwdne145:0"

#. js-lingui-id: Dht9W3
#: src/pages/not-found/NotFound.tsx
msgid "Back to content"
msgstr "crwdns147:0crwdne147:0"

#. js-lingui-id: R+w/Va
#: src/pages/settings/SettingsBilling.tsx
#: src/pages/settings/SettingsBilling.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Billing"
msgstr "crwdns149:0crwdne149:0"

#. js-lingui-id: K1172m
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
msgid "Blocklist"
msgstr "crwdns151:0crwdne151:0"

#. js-lingui-id: 2yl5lQ
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Book a Call"
msgstr "crwdns153:0crwdne153:0"

#. js-lingui-id: 8Pfllj
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "By using Twenty, you agree to the"
msgstr "crwdns155:0crwdne155:0"

#. js-lingui-id: PmmvzS
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterValue.tsx
msgid "Calculate"
msgstr "crwdns157:0crwdne157:0"

#. js-lingui-id: AjVXBS
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Calendar"
msgstr "crwdns159:0crwdne159:0"

#. js-lingui-id: wRpDAv
#: src/modules/command-menu/hooks/useOpenCalendarEventInCommandMenu.ts
msgid "Calendar Event"
msgstr "crwdns5582:0crwdne5582:0"

#. js-lingui-id: wLtx+m
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Calendar settings"
msgstr "crwdns5041:0crwdne5041:0"

#. js-lingui-id: LbIh3Y
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Calendar Sync"
msgstr "crwdns5549:0crwdne5549:0"

#. js-lingui-id: HlJKLT
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
#~ msgid "Calendar Sync Status"
#~ msgstr "Calendar Sync Status"

#. js-lingui-id: EUpfsd
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Calendars"
msgstr "crwdns161:0crwdne161:0"

#. js-lingui-id: msssZq
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Can't change API names for standard objects"
msgstr "crwdns5600:0crwdne5600:0"

#. js-lingui-id: dEgA5A
#: src/pages/auth/Authorize.tsx
#: src/modules/ui/layout/modal/components/ConfirmationModal.tsx
#: src/modules/ui/feedback/snack-bar-manager/components/SnackBar.tsx
#: src/modules/settings/components/SaveAndCancelButtons/CancelButton.tsx
msgid "Cancel"
msgstr "crwdns163:0crwdne163:0"

#. js-lingui-id: 0TllC8
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Cancel anytime"
msgstr "crwdns165:0crwdne165:0"

#. js-lingui-id: rRK/Lf
#: src/pages/settings/SettingsBilling.tsx
msgid "Cancel Plan"
msgstr "crwdns167:0crwdne167:0"

#. js-lingui-id: N6gPiD
#: src/pages/settings/SettingsBilling.tsx
msgid "Cancel your subscription"
msgstr "crwdns169:0crwdne169:0"

#. js-lingui-id: M1RLfx
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Catalan"
msgstr "crwdns1797:0crwdne1797:0"

#. js-lingui-id: OfzMnb
#: src/pages/settings/SettingsBilling.tsx
msgid "Change {to}"
msgstr "crwdns171:0{to}crwdne171:0"

#. js-lingui-id: VhMDMg
#: src/pages/auth/PasswordReset.tsx
#: src/modules/settings/profile/components/ChangePassword.tsx
#: src/modules/settings/profile/components/ChangePassword.tsx
msgid "Change Password"
msgstr "crwdns173:0crwdne173:0"

#. js-lingui-id: SviKkE
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Chinese — Simplified"
msgstr "crwdns175:0crwdne175:0"

#. js-lingui-id: dzb4Ep
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Chinese — Traditional"
msgstr "crwdns177:0crwdne177:0"

#. js-lingui-id: JEFFOR
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Choose an object"
msgstr "crwdns179:0crwdne179:0"

#. js-lingui-id: Qz73jD
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "Choose between OIDC and SAML protocols"
msgstr "crwdns5043:0crwdne5043:0"

#. js-lingui-id: YcrXB2
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
msgid "Choose the default currency that will apply"
msgstr "crwdns5045:0crwdne5045:0"

#. js-lingui-id: LHce7q
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Choose the fields that will identify your records"
msgstr "crwdns3985:0crwdne3985:0"

#. js-lingui-id: KT6rEB
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "Choose your provider"
msgstr "crwdns181:0crwdne181:0"

#. js-lingui-id: 9qP96p
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Choose your Trial"
msgstr "crwdns183:0crwdne183:0"

#. js-lingui-id: xCJdfg
#: src/modules/ui/input/components/internal/date/components/InternalDatePicker.tsx
msgid "Clear"
msgstr "crwdns5047:0crwdne5047:0"

#. js-lingui-id: b9Y4up
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client ID"
msgstr "crwdns5049:0crwdne5049:0"

#. js-lingui-id: Bdj4LI
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client Secret"
msgstr "crwdns5051:0crwdne5051:0"

#. js-lingui-id: XUe4cu
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client Settings"
msgstr "crwdns4889:0crwdne4889:0"

#. js-lingui-id: yz7wBu
#: src/modules/ui/feedback/snack-bar-manager/components/SnackBar.tsx
msgid "Close"
msgstr "crwdns185:0crwdne185:0"

#. js-lingui-id: qYsAlX
#: src/modules/ui/layout/page-header/components/PageHeaderOpenCommandMenuButton.tsx
msgid "Close command menu"
msgstr "crwdns4859:0crwdne4859:0"

#. js-lingui-id: EWPtMO
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "Code"
msgstr "crwdns5602:0crwdne5602:0"

#. js-lingui-id: H86f9p
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
msgid "Collapse"
msgstr "crwdns5381:0crwdne5381:0"

#. js-lingui-id: Xose0w
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Color code"
msgstr "crwdns5053:0crwdne5053:0"

#. js-lingui-id: NM9bMd
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Compact view"
msgstr "crwdns5055:0crwdne5055:0"

#. js-lingui-id: +zUMwJ
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Configure an SSO connection"
msgstr "crwdns187:0crwdne187:0"

#. js-lingui-id: QTNsSm
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Configure and customize your calendar preferences."
msgstr "crwdns189:0crwdne189:0"

#. js-lingui-id: aGwm+D
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Configure how dates are displayed across the app"
msgstr "crwdns191:0crwdne191:0"

#. js-lingui-id: ghdb7+
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Configure how we should display your events in your calendar"
msgstr "crwdns5057:0crwdne5057:0"

#. js-lingui-id: Bh4GBD
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Configure your emails and calendar settings."
msgstr "crwdns193:0crwdne193:0"

#. js-lingui-id: 7VpPHA
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentConfirmationModal.tsx
msgid "Confirm"
msgstr "crwdns1799:0crwdne1799:0"

#. js-lingui-id: D8ATlr
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "Connect a new account to your workspace"
msgstr "crwdns195:0crwdne195:0"

#. js-lingui-id: Zgi9Fd
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "Connect with Google"
msgstr "crwdns197:0crwdne197:0"

#. js-lingui-id: IOfqM8
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "Connect with Microsoft"
msgstr "crwdns199:0crwdne199:0"

#. js-lingui-id: 9TzudL
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Connected accounts"
msgstr "crwdns201:0crwdne201:0"

#. js-lingui-id: Y2y0mC
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Contact auto-creation"
msgstr "crwdns5059:0crwdne5059:0"

#. js-lingui-id: /5mghO
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Contains"
msgstr "crwdns5061:0crwdne5061:0"

#. js-lingui-id: M73whl
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Context"
msgstr "crwdns203:0crwdne203:0"

#. js-lingui-id: xGVfLh
#: src/pages/onboarding/InviteTeam.tsx
#: src/pages/onboarding/CreateWorkspace.tsx
#: src/pages/onboarding/CreateProfile.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/modules/auth/sign-in-up/components/SignInUpWithCredentials.tsx
msgid "Continue"
msgstr "crwdns205:0crwdne205:0"

#. js-lingui-id: RvVi9c
#: src/modules/auth/sign-in-up/components/SignInUpWithCredentials.tsx
msgid "Continue with Email"
msgstr "crwdns207:0crwdne207:0"

#. js-lingui-id: oZyG4C
#: src/modules/auth/sign-in-up/components/SignInUpWithGoogle.tsx
msgid "Continue with Google"
msgstr "crwdns209:0crwdne209:0"

#. js-lingui-id: ztoybH
#: src/modules/auth/sign-in-up/components/SignInUpWithMicrosoft.tsx
msgid "Continue with Microsoft"
msgstr "crwdns211:0crwdne211:0"

#. js-lingui-id: u+VWhB
#: src/pages/settings/workspace/SettingsCustomDomainRecords.tsx
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerWebhookForm.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminEnvCopyableText.tsx
msgid "Copied to clipboard!"
msgstr "crwdns4891:0crwdne4891:0"

#. js-lingui-id: CcGOj+
#: src/modules/command-menu/components/CommandMenu.tsx
#~ msgid "Copilot"
#~ msgstr "Copilot"

#. js-lingui-id: he3ygx
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
#: src/modules/settings/developers/components/ApiKeyInput.tsx
msgid "Copy"
msgstr "crwdns4893:0crwdne4893:0"

#. js-lingui-id: 7eVkEH
#: src/pages/onboarding/InviteTeam.tsx
msgid "Copy invitation link"
msgstr "crwdns215:0crwdne215:0"

#. js-lingui-id: y1eoq1
#: src/modules/workspace/components/WorkspaceInviteLink.tsx
msgid "Copy link"
msgstr "crwdns3987:0crwdne3987:0"

#. js-lingui-id: eZ5HO9
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Copy link to view"
msgstr "crwdns5519:0crwdne5519:0"

#. js-lingui-id: Ej5euX
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Copy this key as it will not be visible again"
msgstr "crwdns217:0crwdne217:0"

#. js-lingui-id: SGsgDO
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Core"
msgstr "crwdns5441:0crwdne5441:0"

#. js-lingui-id: wBMjJ2
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "Count"
msgstr "crwdns219:0crwdne219:0"

#. js-lingui-id: EkZfen
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count all"
msgstr "crwdns221:0crwdne221:0"

#. js-lingui-id: vQJINq
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count empty"
msgstr "crwdns223:0crwdne223:0"

#. js-lingui-id: sLDQbp
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count false"
msgstr "crwdns5543:0crwdne5543:0"

#. js-lingui-id: DzRsDJ
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count not empty"
msgstr "crwdns225:0crwdne225:0"

#. js-lingui-id: ft6dHY
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count true"
msgstr "crwdns5545:0crwdne5545:0"

#. js-lingui-id: 9FZBbf
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count unique values"
msgstr "crwdns227:0crwdne227:0"

#. js-lingui-id: hYgDIe
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
msgid "Create"
msgstr "crwdns5063:0crwdne5063:0"

#. js-lingui-id: zNoOC2
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Create a workflow and return here to view its versions"
msgstr "crwdns229:0crwdne229:0"

#. js-lingui-id: uXGLuq
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Create API key"
msgstr "crwdns231:0crwdne231:0"

#. js-lingui-id: d0DCww
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Create new record"
msgstr "crwdns233:0crwdne233:0"

#. js-lingui-id: gSyzEV
#: src/pages/onboarding/CreateProfile.tsx
msgid "Create profile"
msgstr "crwdns235:0crwdne235:0"

#. js-lingui-id: RoyYUE
#: src/modules/settings/roles/components/Roles.tsx
msgid "Create Role"
msgstr "crwdns237:0crwdne237:0"

#. js-lingui-id: 6MP9lc
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
msgid "Create select field"
msgstr "crwdns5065:0crwdne5065:0"

#. js-lingui-id: zt6Erc
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
#: src/modules/views/components/UpdateViewButtonGroup.tsx
msgid "Create view"
msgstr "crwdns5067:0crwdne5067:0"

#. js-lingui-id: dkAPxi
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
msgid "Create Webhook"
msgstr "crwdns239:0crwdne239:0"

#. js-lingui-id: kdLGki
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Create Workspace"
msgstr "crwdns5563:0crwdne5563:0"

#. js-lingui-id: 9chYz/
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Create your workspace"
msgstr "crwdns241:0crwdne241:0"

#. js-lingui-id: d+F6q9
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Created"
msgstr "crwdns5069:0crwdne5069:0"

#. js-lingui-id: Hd06oy
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "Current version"
msgstr "crwdns5531:0crwdne5531:0"

#. js-lingui-id: p76aoR
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
#~ msgid "Current version:"
#~ msgstr "Current version:"

#. js-lingui-id: XQ681Q
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Custom Domain"
msgstr "crwdns4847:0crwdne4847:0"

#. js-lingui-id: 1GTWIA
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Custom domain updated"
msgstr "crwdns5361:0crwdne5361:0"

#. js-lingui-id: 8skTDV
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Custom objects"
msgstr "crwdns243:0crwdne243:0"

#. js-lingui-id: qt+EaC
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
msgid "Customise the fields available in the {objectLabelSingular} views."
msgstr "crwdns245:0{objectLabelSingular}crwdne245:0"

#. js-lingui-id: CMhr4u
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Customize Domain"
msgstr "crwdns247:0crwdne247:0"

#. js-lingui-id: RP2we8
#: src/modules/object-record/record-table/record-table-header/components/RecordTableHeaderPlusButtonContent.tsx
msgid "Customize fields"
msgstr "crwdns5071:0crwdne5071:0"

#. js-lingui-id: bCJa9l
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Customize your workspace security"
msgstr "crwdns249:0crwdne249:0"

#. js-lingui-id: w9VTXG
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Czech"
msgstr "crwdns1801:0crwdne1801:0"

#. js-lingui-id: Zz6Cxn
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Danger zone"
msgstr "crwdns251:0crwdne251:0"

#. js-lingui-id: Fo2vDn
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Danish"
msgstr "crwdns1803:0crwdne1803:0"

#. js-lingui-id: pvnfJD
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Dark"
msgstr "crwdns3989:0crwdne3989:0"

#. js-lingui-id: TtG/MN
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Data deletion"
msgstr "crwdns5073:0crwdne5073:0"

#. js-lingui-id: 5cNMFz
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Data model"
msgstr "crwdns253:0crwdne253:0"

#. js-lingui-id: r+cVRP
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Data type"
msgstr "crwdns255:0crwdne255:0"

#. js-lingui-id: mYGY3B
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownContent.tsx
msgid "Date"
msgstr "crwdns257:0crwdne257:0"

#. js-lingui-id: Ud9zHv
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Date and time"
msgstr "crwdns259:0crwdne259:0"

#. js-lingui-id: Lhd0oQ
#: src/pages/settings/profile/appearance/components/DateTimeSettingsDateFormatSelect.tsx
msgid "Date format"
msgstr "crwdns5075:0crwdne5075:0"

#. js-lingui-id: 5y3O+A
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Deactivate"
msgstr "crwdns261:0crwdne261:0"

#. js-lingui-id: qk4i22
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Deactivate \"Synchronize Objects Labels and API Names\" to set a custom API name"
msgstr "crwdns263:0crwdne263:0"

#. js-lingui-id: T2YbXF
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Deactivate object"
msgstr "crwdns3991:0crwdne3991:0"

#. js-lingui-id: gexAq8
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
msgid "Deactivate this field"
msgstr "crwdns265:0crwdne265:0"

#. js-lingui-id: 4tpC8V
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Deactivate Workflow"
msgstr "crwdns267:0crwdne267:0"

#. js-lingui-id: yAT3be
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "Deactivated"
msgstr "crwdns5077:0crwdne5077:0"

#. js-lingui-id: mC21D6
#: src/modules/settings/data-model/fields/forms/address/components/SettingsDataModelFieldAddressForm.tsx
msgid "Default Country"
msgstr "crwdns5079:0crwdne5079:0"

#. js-lingui-id: 4zuPQL
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "Default Country Code"
msgstr "crwdns5081:0crwdne5081:0"

#. js-lingui-id: aQ8swY
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
msgid "Default Value"
msgstr "crwdns5083:0crwdne5083:0"

#. js-lingui-id: Y2ImVJ
#: src/pages/settings/data-model/SettingsNewObject.tsx
msgid "Define the name and description of your object"
msgstr "crwdns269:0crwdne269:0"

#. js-lingui-id: bQkkFU
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Define what will be visible to other users in your workspace"
msgstr "crwdns5085:0crwdne5085:0"

#. js-lingui-id: cnGeoo
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuDropdown.stories.tsx
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuBarEntry.stories.tsx
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuBarEntry.stories.tsx
#: src/modules/action-menu/components/__stories__/CommandMenuActionMenuDropdown.stories.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Delete"
msgstr "crwdns271:0crwdne271:0"

#. js-lingui-id: ZDGm40
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Delete account"
msgstr "crwdns273:0crwdne273:0"

#. js-lingui-id: gAz0S5
#: src/modules/settings/profile/components/DeleteAccount.tsx
msgid "Delete account and all the associated data"
msgstr "crwdns275:0crwdne275:0"

#. js-lingui-id: hGfWDm
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Delete API key"
msgstr "crwdns277:0crwdne277:0"

#. js-lingui-id: 4dpwsE
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Delete record"
msgstr "crwdns279:0crwdne279:0"

#. js-lingui-id: kf0A63
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Delete records"
msgstr "crwdns281:0crwdne281:0"

#. js-lingui-id: T6S2Ns
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Delete this integration"
msgstr "crwdns283:0crwdne283:0"

#. js-lingui-id: aRG49z
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Delete view"
msgstr "crwdns5521:0crwdne5521:0"

#. js-lingui-id: snMaH4
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Delete webhook"
msgstr "crwdns285:0crwdne285:0"

#. js-lingui-id: UA2IpC
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Delete workflow"
msgstr "crwdns287:0crwdne287:0"

#. js-lingui-id: ABwG9x
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Delete workflows"
msgstr "crwdns289:0crwdne289:0"

#. js-lingui-id: kYu0eF
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Delete workspace"
msgstr "crwdns291:0crwdne291:0"

#. js-lingui-id: mk2Ygs
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Delete your whole workspace"
msgstr "crwdns293:0crwdne293:0"

#. js-lingui-id: vGjmyl
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Deleted"
msgstr "crwdns5087:0crwdne5087:0"

#. js-lingui-id: kcGoDz
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#~ msgid "Deleted {objectNamePlural}"
#~ msgstr "Deleted {objectNamePlural}"

#. js-lingui-id: WH/5rN
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Deleted records"
msgstr "crwdns5375:0crwdne5375:0"

#. js-lingui-id: /TC7qI
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Deleted runs"
msgstr "crwdns5383:0crwdne5383:0"

#. js-lingui-id: Wj5mzm
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Deleted versions"
msgstr "crwdns5385:0crwdne5385:0"

#. js-lingui-id: gw3Tlm
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Deleted workflows"
msgstr "crwdns5387:0crwdne5387:0"

#. js-lingui-id: Cko536
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
msgid "Descending"
msgstr "crwdns295:0crwdne295:0"

#. js-lingui-id: Nu4oKW
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissionsSettingsTableHeader.tsx
msgid "Description"
msgstr "crwdns297:0crwdne297:0"

#. js-lingui-id: 2xxBws
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Destroy"
msgstr "crwdns299:0crwdne299:0"

#. js-lingui-id: n+SX4g
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Developers"
msgstr "crwdns301:0crwdne301:0"

#. js-lingui-id: zAg2B9
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Discard Draft"
msgstr "crwdns303:0crwdne303:0"

#. js-lingui-id: Xm/s+u
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Display"
msgstr "crwdns5091:0crwdne5091:0"

#. js-lingui-id: 8Vg8H7
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Display as a plain number or a percentage"
msgstr "crwdns5093:0crwdne5093:0"

#. js-lingui-id: i66xz9
#: src/modules/settings/data-model/fields/forms/date/components/SettingsDataModelFieldDateForm.tsx
msgid "Display as relative date"
msgstr "crwdns305:0crwdne305:0"

#. js-lingui-id: GoHpxA
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "Display text on multiple lines"
msgstr "crwdns5095:0crwdne5095:0"

#. js-lingui-id: U9q4zF
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Doesn't contain"
msgstr "crwdns5097:0crwdne5097:0"

#. js-lingui-id: EoKe5U
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Domain"
msgstr "crwdns307:0crwdne307:0"

#. js-lingui-id: yGpVSw
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Domain added successfully."
msgstr "crwdns4895:0crwdne4895:0"

#. js-lingui-id: 7kVRe6
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Domains have to be smaller than 256 characters, cannot contain spaces and cannot contain any special characters."
msgstr "crwdns5369:0crwdne5369:0"

#. js-lingui-id: KUxVkp
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Don’t auto-create contacts."
msgstr "crwdns5099:0crwdne5099:0"

#. js-lingui-id: cx0Ws8
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Don’t create contacts from/to Gmail, Outlook emails"
msgstr "crwdns5101:0crwdne5101:0"

#. js-lingui-id: 3qDEYI
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Don’t sync emails from team@ support@ noreply@..."
msgstr "crwdns5103:0crwdne5103:0"

#. js-lingui-id: WcWS//
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Download file"
msgstr "crwdns4897:0crwdne4897:0"

#. js-lingui-id: KIjvtr
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Dutch"
msgstr "crwdns1805:0crwdne1805:0"

#. js-lingui-id: QVVmxi
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "E.g. backoffice integration"
msgstr "crwdns309:0crwdne309:0"

#. js-lingui-id: tOkc8o
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Earliest"
msgstr "crwdns311:0crwdne311:0"

#. js-lingui-id: JTbQuO
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Earliest date"
msgstr "crwdns313:0crwdne313:0"

#. js-lingui-id: ePK91l
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
msgid "Edit"
msgstr "crwdns5105:0crwdne5105:0"

#. js-lingui-id: v+uKyy
#: src/pages/settings/SettingsBilling.tsx
msgid "Edit billing interval"
msgstr "crwdns315:0crwdne315:0"

#. js-lingui-id: 9QCQIc
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenRecordGroupsContent.tsx
msgid "Edit field values"
msgstr "crwdns5107:0crwdne5107:0"

#. js-lingui-id: oKQ7ls
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
msgid "Edit Fields"
msgstr "crwdns5109:0crwdne5109:0"

#. js-lingui-id: h2KoTu
#: src/pages/settings/SettingsBilling.tsx
msgid "Edit payment method, see your invoices and more"
msgstr "crwdns317:0crwdne317:0"

#. js-lingui-id: 6o1M/Q
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Edit your subdomain name or set a custom domain."
msgstr "crwdns319:0crwdne319:0"

#. js-lingui-id: O3oNi5
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/auth/PasswordReset.tsx
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentTableHeader.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Email"
msgstr "crwdns321:0crwdne321:0"

#. js-lingui-id: 2SWjdJ
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#~ msgid "Email can not be empty"
#~ msgstr "Email can not be empty"

#. js-lingui-id: unEEog
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Email cannot be empty"
msgstr "crwdns5371:0crwdne5371:0"

#. js-lingui-id: lfQsvW
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Email integration"
msgstr "crwdns323:0crwdne323:0"

#. js-lingui-id: QT/Wo7
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Email or domain is already in blocklist"
msgstr "crwdns325:0crwdne325:0"

#. js-lingui-id: LimKOG
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Email verification"
msgstr "crwdns5111:0crwdne5111:0"

#. js-lingui-id: TBv/iZ
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Email verification failed."
msgstr "crwdns4901:0crwdne4901:0"

#. js-lingui-id: svTijF
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
msgid "Email verification link resent!"
msgstr "crwdns4903:0crwdne4903:0"

#. js-lingui-id: 00icDW
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Email verified."
msgstr "crwdns4905:0crwdne4905:0"

#. js-lingui-id: VI2hiF
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistTable.tsx
msgid "Email/Domain"
msgstr "crwdns5113:0crwdne5113:0"

#. js-lingui-id: BXEcos
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Emails"
msgstr "crwdns327:0crwdne327:0"

#. js-lingui-id: Ww/M6X
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Emails settings"
msgstr "crwdns5115:0crwdne5115:0"

#. js-lingui-id: eXoH4Q
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "employees"
msgstr "crwdns329:0crwdne329:0"

#. js-lingui-id: gqv5ZL
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Employees"
msgstr "crwdns331:0crwdne331:0"

#. js-lingui-id: N2S1rs
#: src/modules/object-record/record-inline-cell/components/RecordInlineCellDisplayMode.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/activities/timeline-activities/rows/main-object/components/EventFieldDiff.tsx
msgid "Empty"
msgstr "crwdns333:0crwdne333:0"

#. js-lingui-id: OMbipf
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
msgid "Empty Array"
msgstr "crwdns5511:0crwdne5511:0"

#. js-lingui-id: 3hSGoJ
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
msgid "Empty Object"
msgstr "crwdns5513:0crwdne5513:0"

#. js-lingui-id: T3juzf
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Endpoint URL"
msgstr "crwdns335:0crwdne335:0"

#. js-lingui-id: lYGfRP
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "English"
msgstr "crwdns337:0crwdne337:0"

#. js-lingui-id: /bfFKe
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Enjoy a {withCreditCardTrialPeriodDuration}-days free trial"
msgstr "crwdns339:0{withCreditCardTrialPeriodDuration}crwdne339:0"

#. js-lingui-id: XJU8BD
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Enter the credentials to set the connection"
msgstr "crwdns4907:0crwdne4907:0"

#. js-lingui-id: rGWgcm
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Enter the infos to set the connection"
msgstr "crwdns4909:0crwdne4909:0"

#. js-lingui-id: rYIISB
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Enter user ID or email address"
msgstr "crwdns5117:0crwdne5117:0"

#. js-lingui-id: GpB8YV
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Enterprise"
msgstr "crwdns341:0crwdne341:0"

#. js-lingui-id: 8PrrNJ
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Entity ID copied to clipboard"
msgstr "crwdns4911:0crwdne4911:0"

#. js-lingui-id: 7RnAWe
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "Env Variables"
msgstr "crwdns5119:0crwdne5119:0"

#. js-lingui-id: XOEl9R
#: src/modules/object-record/record-field/meta-types/display/components/PhonesFieldDisplay.tsx
msgid "Error copying to clipboard"
msgstr "crwdns4913:0crwdne4913:0"

#. js-lingui-id: c3qGJX
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Error deleting api key: {err}"
msgstr "crwdns343:0{err}crwdne343:0"

#. js-lingui-id: QnVLjD
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Error deleting invitation"
msgstr "crwdns4915:0crwdne4915:0"

#. js-lingui-id: cyvTSq
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
msgid "Error deleting SSO Identity Provider"
msgstr "crwdns4917:0crwdne4917:0"

#. js-lingui-id: WEltn2
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
msgid "Error editing SSO Identity Provider"
msgstr "crwdns4919:0crwdne4919:0"

#. js-lingui-id: bj7nh3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Error regenerating api key: {err}"
msgstr "crwdns345:0{err}crwdne345:0"

#. js-lingui-id: clfpgU
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Error resending invitation"
msgstr "crwdns4921:0crwdne4921:0"

#. js-lingui-id: VSQxWH
#: src/pages/settings/SettingsBilling.tsx
msgid "Error while switching subscription {to}."
msgstr "crwdns347:0{to}crwdne347:0"

#. js-lingui-id: JLxMta
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
msgid "Establish Webhook endpoints for notifications on asynchronous events."
msgstr "crwdns349:0crwdne349:0"

#. js-lingui-id: poC90w
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Event visibility"
msgstr "crwdns5121:0crwdne5121:0"

#. js-lingui-id: jvNRZW
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Events you participated in are displayed in red."
msgstr "crwdns5123:0crwdne5123:0"

#. js-lingui-id: wqF3jl
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Everything"
msgstr "crwdns5125:0crwdne5125:0"

#. js-lingui-id: QQlMid
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Exclude group emails"
msgstr "crwdns5127:0crwdne5127:0"

#. js-lingui-id: +tk2yZ
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Exclude non-professional emails"
msgstr "crwdns5129:0crwdne5129:0"

#. js-lingui-id: cIgBjB
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
#~ msgid "Exclude the following people and domains from my email sync"
#~ msgstr "Exclude the following people and domains from my email sync"

#. js-lingui-id: yhhfqh
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
msgid "Exclude the following people and domains from my email sync. Internal conversations will not be imported"
msgstr "crwdns4863:0crwdne4863:0"

#. js-lingui-id: fV7V51
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Existing objects"
msgstr "crwdns353:0crwdne353:0"

#. js-lingui-id: LFNXuj
#: src/modules/ui/layout/fullscreen/components/FullScreenContainer.tsx
msgid "Exit Full Screen"
msgstr "crwdns5443:0crwdne5443:0"

#. js-lingui-id: IZ4o2e
#: src/modules/navigation/components/AppNavigationDrawer.tsx
msgid "Exit Settings"
msgstr "crwdns355:0crwdne355:0"

#. js-lingui-id: 1A3EXy
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
msgid "Expand"
msgstr "crwdns5389:0crwdne5389:0"

#. js-lingui-id: tXGQvS
#: src/modules/workflow/workflow-diagram/components/WorkflowDiagramCanvasEditableEffect.tsx
#~ msgid "Expected selected node to be a create step node."
#~ msgstr "Expected selected node to be a create step node."

#. js-lingui-id: bKBhgb
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Experience"
msgstr "crwdns357:0crwdne357:0"

#. js-lingui-id: LxRNPw
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/settings/developers/components/SettingsApiKeysTable.tsx
msgid "Expiration"
msgstr "crwdns359:0crwdne359:0"

#. js-lingui-id: SkXfL0
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "Expiration Date"
msgstr "crwdns361:0crwdne361:0"

#. js-lingui-id: M1RnFv
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Expired"
msgstr "crwdns4923:0crwdne4923:0"

#. js-lingui-id: i9qiyR
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Expires in"
msgstr "crwdns363:0crwdne363:0"

#. js-lingui-id: GS+Mus
#: src/modules/object-record/record-index/export/hooks/useExportRecords.ts
#: src/modules/action-menu/components/__stories__/CommandMenuActionMenuDropdown.stories.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Export"
msgstr "crwdns365:0crwdne365:0"

#. js-lingui-id: G5DJkP
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Export record"
msgstr "crwdns5391:0crwdne5391:0"

#. js-lingui-id: ep2rbf
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Export records"
msgstr "crwdns367:0crwdne367:0"

#. js-lingui-id: iHK6np
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Export run"
msgstr "crwdns5557:0crwdne5557:0"

#. js-lingui-id: vwtAUW
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Export runs"
msgstr "crwdns5393:0crwdne5393:0"

#. js-lingui-id: q46CjD
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Export to PDF"
msgstr "crwdns369:0crwdne369:0"

#. js-lingui-id: BuprEs
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Export version"
msgstr "crwdns5559:0crwdne5559:0"

#. js-lingui-id: 4FLUle
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Export versions"
msgstr "crwdns5395:0crwdne5395:0"

#. js-lingui-id: DaGxE0
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Export view"
msgstr "crwdns371:0crwdne371:0"

#. js-lingui-id: +FMXdE
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Export workflow"
msgstr "crwdns5397:0crwdne5397:0"

#. js-lingui-id: XcAij/
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Export workflows"
msgstr "crwdns5399:0crwdne5399:0"

#. js-lingui-id: eWCNmu
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#~ msgid "Export Workflows"
#~ msgstr "Export Workflows"

#. js-lingui-id: jARNNi
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Failed to validate API key. Please check your API key and try again."
msgstr "crwdns5445:0crwdne5445:0"

#. js-lingui-id: ocUvR+
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "False"
msgstr "crwdns5131:0crwdne5131:0"

#. js-lingui-id: X9kySA
#: src/modules/favorites/components/CurrentWorkspaceMemberFavoritesFolders.tsx
msgid "Favorites"
msgstr "crwdns375:0crwdne375:0"

#. js-lingui-id: YXjpZx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Feature Flag"
msgstr "crwdns5133:0crwdne5133:0"

#. js-lingui-id: kP/brT
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Feature Flags & Impersonation"
msgstr "crwdns5135:0crwdne5135:0"

#. js-lingui-id: nrXDdR
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Field name"
msgstr "crwdns5137:0crwdne5137:0"

#. js-lingui-id: zXgopL
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Field type"
msgstr "crwdns377:0crwdne377:0"

#. js-lingui-id: vF68cg
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Fields"
msgstr "crwdns379:0crwdne379:0"

#. js-lingui-id: 3w/aqw
#: src/testing/mock-data/tableData.ts
msgid "Fields Count"
msgstr "crwdns381:0crwdne381:0"

#. js-lingui-id: o7J4JM
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/object-filter-dropdown/components/SingleEntityObjectFilterDropdownButton.tsx
#: src/modules/object-record/object-filter-dropdown/components/MultipleFiltersButton.tsx
msgid "Filter"
msgstr "crwdns383:0crwdne383:0"

#. js-lingui-id: cSev+j
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Filters"
msgstr "crwdns385:0crwdne385:0"

#. js-lingui-id: USZ2N6
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Finnish"
msgstr "crwdns1807:0crwdne1807:0"

#. js-lingui-id: ZyIk6Y
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "First 10 lines"
msgstr "crwdns5139:0crwdne5139:0"

#. js-lingui-id: I3hko2
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "First 2 lines"
msgstr "crwdns5141:0crwdne5141:0"

#. js-lingui-id: BDDkm3
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "First 5 lines"
msgstr "crwdns5143:0crwdne5143:0"

#. js-lingui-id: kODvZJ
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/profile/components/NameFields.tsx
msgid "First Name"
msgstr "crwdns387:0crwdne387:0"

#. js-lingui-id: glx6on
#: src/modules/auth/sign-in-up/components/SignInUpWorkspaceScopeForm.tsx
msgid "Forgot your password?"
msgstr "crwdns389:0crwdne389:0"

#. js-lingui-id: nLC6tu
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "French"
msgstr "crwdns391:0crwdne391:0"

#. js-lingui-id: aTieE0
#: src/pages/settings/SettingsBilling.tsx
msgid "from monthly to yearly"
msgstr "crwdns393:0crwdne393:0"

#. js-lingui-id: K04lE5
#: src/pages/settings/SettingsBilling.tsx
msgid "from yearly to monthly"
msgstr "crwdns395:0crwdne395:0"

#. js-lingui-id: scmRyR
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Full access"
msgstr "crwdns397:0crwdne397:0"

#. js-lingui-id: xANKBj
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Functions"
msgstr "crwdns399:0crwdne399:0"

#. js-lingui-id: Weq9zb
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "General"
msgstr "crwdns401:0crwdne401:0"

#. js-lingui-id: DDcvSo
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "German"
msgstr "crwdns403:0crwdne403:0"

#. js-lingui-id: NXEW3h
#: src/pages/onboarding/InviteTeam.tsx
msgid "Get the most out of your workspace by inviting your team."
msgstr "crwdns405:0crwdne405:0"

#. js-lingui-id: zSGbaR
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Get your subscription"
msgstr "crwdns407:0crwdne407:0"

#. js-lingui-id: 2GT3Hf
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Global"
msgstr "crwdns409:0crwdne409:0"

#. js-lingui-id: iS69s6
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Go to runs"
msgstr "crwdns5604:0crwdne5604:0"

#. js-lingui-id: mT57+Q
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
msgid "Go to Settings"
msgstr "crwdns5145:0crwdne5145:0"

#. js-lingui-id: A5WHZY
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Go to workflows"
msgstr "crwdns5606:0crwdne5606:0"

#. js-lingui-id: hWp1MY
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Grant Twenty support temporary access to your workspace so we can troubleshoot problems or recover content on your behalf. You can revoke access at any time."
msgstr "crwdns411:0crwdne411:0"

#. js-lingui-id: gBiL6J
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "GraphQL"
msgstr "crwdns5447:0crwdne5447:0"

#. js-lingui-id: /YalkJ
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
msgid "GraphQL API Playground"
msgstr "crwdns5449:0crwdne5449:0"

#. js-lingui-id: JXaffl
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Greater than"
msgstr "crwdns5147:0crwdne5147:0"

#. js-lingui-id: CZXzs4
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Greek"
msgstr "crwdns1809:0crwdne1809:0"

#. js-lingui-id: ALoP4W
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Group by"
msgstr "crwdns5149:0crwdne5149:0"

#. js-lingui-id: I1IOmb
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/modules/settings/admin-panel/health-status/components/SettingsAdminHealthStatus.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "Health Status"
msgstr "crwdns4855:0crwdne4855:0"

#. js-lingui-id: 3oTCgM
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Hebrew"
msgstr "crwdns1811:0crwdne1811:0"

#. js-lingui-id: D+zLDD
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
msgid "Hidden"
msgstr "crwdns5151:0crwdne5151:0"

#. js-lingui-id: oK+1Wj
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Hidden Fields"
msgstr "crwdns5153:0crwdne5153:0"

#. js-lingui-id: vLyv1R
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
msgid "Hide"
msgstr "crwdns413:0crwdne413:0"

#. js-lingui-id: OlbYor
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Hide deleted"
msgstr "crwdns5620:0crwdne5620:0"

#. js-lingui-id: FHhlye
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Hide deleted records"
msgstr "crwdns5622:0crwdne5622:0"

#. js-lingui-id: 0dZtKR
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Hide deleted runs"
msgstr "crwdns5624:0crwdne5624:0"

#. js-lingui-id: Jc6FrI
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Hide deleted versions"
msgstr "crwdns5626:0crwdne5626:0"

#. js-lingui-id: aOZAIB
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Hide deleted workflows"
msgstr "crwdns5628:0crwdne5628:0"

#. js-lingui-id: HS8BG/
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Hide empty groups"
msgstr "crwdns5155:0crwdne5155:0"

#. js-lingui-id: B06Bgk
#: src/pages/onboarding/CreateProfile.tsx
msgid "How you'll be identified on the app."
msgstr "crwdns415:0crwdne415:0"

#. js-lingui-id: k7iMla
#: src/modules/settings/admin-panel/health-status/components/SettingsAdminHealthStatus.tsx
msgid "How your system is doing"
msgstr "crwdns5471:0crwdne5471:0"

#. js-lingui-id: mkWad2
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Hungarian"
msgstr "crwdns1813:0crwdne1813:0"

#. js-lingui-id: wwu18a
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Icon"
msgstr "crwdns5157:0crwdne5157:0"

#. js-lingui-id: XTWO+W
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Icon and Name"
msgstr "crwdns417:0crwdne417:0"

#. js-lingui-id: S0kLOH
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "ID"
msgstr "crwdns5533:0crwdne5533:0"

#. js-lingui-id: sJGljQ
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Identifier"
msgstr "crwdns419:0crwdne419:0"

#. js-lingui-id: 06cbfQ
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Identity Provider"
msgstr "crwdns4925:0crwdne4925:0"

#. js-lingui-id: LPN8Ma
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Identity Provider Metadata XML"
msgstr "crwdns4927:0crwdne4927:0"

#. js-lingui-id: j843N3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "If you’ve lost this key, you can regenerate it, but be aware that any script using this key will need to be updated. Please type\"{confirmationValue}\" to confirm."
msgstr "crwdns421:0{confirmationValue}crwdne421:0"

#. js-lingui-id: tSVr6t
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Impersonate"
msgstr "crwdns5159:0crwdne5159:0"

#. js-lingui-id: l3s5ri
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Import"
msgstr "crwdns5161:0crwdne5161:0"

#. js-lingui-id: eECp4f
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Import records"
msgstr "crwdns5401:0crwdne5401:0"

#. js-lingui-id: ZsIcZZ
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Import workflows"
msgstr "crwdns5403:0crwdne5403:0"

#. js-lingui-id: RqSvT0
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Importing"
msgstr "crwdns5163:0crwdne5163:0"

#. js-lingui-id: NoNwIX
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Inactive"
msgstr "crwdns423:0crwdne423:0"

#. js-lingui-id: pZ/USH
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
msgid "Indexes"
msgstr "crwdns425:0crwdne425:0"

#. js-lingui-id: JE2tjr
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Input must be in camel case and cannot start with a number"
msgstr "crwdns427:0crwdne427:0"

#. js-lingui-id: /6i28D
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormFieldSettings.tsx
msgid "Input settings"
msgstr "crwdns5586:0crwdne5586:0"

#. js-lingui-id: AwUsnG
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
msgid "Instances"
msgstr "crwdns429:0crwdne429:0"

#. js-lingui-id: nbfdhU
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Integrations"
msgstr "crwdns431:0crwdne431:0"

#. js-lingui-id: NtFk/Z
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Invalid auth provider"
msgstr "crwdns433:0crwdne433:0"

#. js-lingui-id: qcXnvu
#: src/pages/settings/workspace/SettingsDomain.tsx
#~ msgid "Invalid custom domain. Custom domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."
#~ msgstr "Invalid custom domain. Custom domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."

#. js-lingui-id: P3qQyo
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid custom domain. Please include at least one subdomain (e.g., sub.example.com)."
msgstr "crwdns5349:0crwdne5349:0"

#. js-lingui-id: u3hwhx
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid domain. Domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."
msgstr "crwdns4929:0crwdne4929:0"

#. js-lingui-id: B2Tpo0
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
msgid "Invalid email"
msgstr "crwdns435:0crwdne435:0"

#. js-lingui-id: /m52AE
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Invalid email or domain"
msgstr "crwdns437:0crwdne437:0"

#. js-lingui-id: b2B7Ze
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Invalid email verification link."
msgstr "crwdns4931:0crwdne4931:0"

#. js-lingui-id: uzxr9u
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Invalid File"
msgstr "crwdns4933:0crwdne4933:0"

#. js-lingui-id: QdoUFL
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid form values"
msgstr "crwdns439:0crwdne439:0"

#. js-lingui-id: K8XJhc
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
msgid "Invalid workspace"
msgstr "crwdns5379:0crwdne5379:0"

#. js-lingui-id: MFKlMB
#: src/modules/workspace/components/WorkspaceInviteTeam.tsx
msgid "Invite"
msgstr "crwdns3993:0crwdne3993:0"

#. js-lingui-id: 0M8+El
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Invite by email"
msgstr "crwdns441:0crwdne441:0"

#. js-lingui-id: PWIq/W
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Invite by link"
msgstr "crwdns443:0crwdne443:0"

#. js-lingui-id: 3athPG
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Invite by Link"
msgstr "crwdns445:0crwdne445:0"

#. js-lingui-id: 5IfmKA
#: src/pages/onboarding/InviteTeam.tsx
msgid "Invite link sent to email addresses"
msgstr "crwdns447:0crwdne447:0"

#. js-lingui-id: x1m5RZ
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Invite user"
msgstr "crwdns5565:0crwdne5565:0"

#. js-lingui-id: d+Y+rP
#: src/pages/onboarding/InviteTeam.tsx
msgid "Invite your team"
msgstr "crwdns449:0crwdne449:0"

#. js-lingui-id: W153SA
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is"
msgstr "crwdns5165:0crwdne5165:0"

#. js-lingui-id: N73SBG
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is after"
msgstr "crwdns5167:0crwdne5167:0"

#. js-lingui-id: NgIlDJ
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is before"
msgstr "crwdns5169:0crwdne5169:0"

#. js-lingui-id: Hte7bc
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is empty"
msgstr "crwdns5171:0crwdne5171:0"

#. js-lingui-id: glSyvW
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is in future"
msgstr "crwdns5173:0crwdne5173:0"

#. js-lingui-id: F9Vw4E
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is in past"
msgstr "crwdns5175:0crwdne5175:0"

#. js-lingui-id: 5pz6vU
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not"
msgstr "crwdns5177:0crwdne5177:0"

#. js-lingui-id: ldI7NO
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not empty"
msgstr "crwdns5179:0crwdne5179:0"

#. js-lingui-id: HQFVAU
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not null"
msgstr "crwdns5181:0crwdne5181:0"

#. js-lingui-id: jPtV7x
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is relative"
msgstr "crwdns5183:0crwdne5183:0"

#. js-lingui-id: 0TLhix
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is today"
msgstr "crwdns5185:0crwdne5185:0"

#. js-lingui-id: IhCN5p
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Issuer URI"
msgstr "crwdns5187:0crwdne5187:0"

#. js-lingui-id: Lj7sBL
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Italian"
msgstr "crwdns451:0crwdne451:0"

#. js-lingui-id: dFtidv
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Japanese"
msgstr "crwdns453:0crwdne453:0"

#. js-lingui-id: OGXtL8
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "Kanban"
msgstr "crwdns5189:0crwdne5189:0"

#. js-lingui-id: h6S9Yz
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Korean"
msgstr "crwdns455:0crwdne455:0"

#. js-lingui-id: zrpwCd
#: src/pages/settings/lab/SettingsLab.tsx
#: src/pages/settings/lab/SettingsLab.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Lab"
msgstr "crwdns457:0crwdne457:0"

#. js-lingui-id: vXIe7J
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Language"
msgstr "crwdns459:0crwdne459:0"

#. js-lingui-id: DU27lO
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Last 1 hour"
msgstr "crwdns5473:0crwdne5473:0"

#. js-lingui-id: 0LzSIl
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 1 Hour (oldest → newest)"
msgstr "crwdns5475:0crwdne5475:0"

#. js-lingui-id: XUlP/Y
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Last 12 hours"
msgstr "crwdns5477:0crwdne5477:0"

#. js-lingui-id: n7EOpf
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 12 Hours (oldest → newest)"
msgstr "crwdns5479:0crwdne5479:0"

#. js-lingui-id: j5nqnO
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 24 Hours (oldest → newest)"
msgstr "crwdns5481:0crwdne5481:0"

#. js-lingui-id: gdAJ4I
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Last 4 hours"
msgstr "crwdns5483:0crwdne5483:0"

#. js-lingui-id: MjAVB0
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 4 Hours (oldest → newest)"
msgstr "crwdns5485:0crwdne5485:0"

#. js-lingui-id: GQgBSj
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 7 Days (oldest → newest)"
msgstr "crwdns5487:0crwdne5487:0"

#. js-lingui-id: UXBCwc
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/profile/components/NameFields.tsx
msgid "Last Name"
msgstr "crwdns461:0crwdne461:0"

#. js-lingui-id: wL3cK8
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Latest"
msgstr "crwdns463:0crwdne463:0"

#. js-lingui-id: Kcjbmz
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Latest date"
msgstr "crwdns465:0crwdne465:0"

#. js-lingui-id: zUzYWu
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "Latest version"
msgstr "crwdns5535:0crwdne5535:0"

#. js-lingui-id: 7dWjtB
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
#~ msgid "Latest version:"
#~ msgstr "Latest version:"

#. js-lingui-id: ZEP8tT
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Launch"
msgstr "crwdns5451:0crwdne5451:0"

#. js-lingui-id: rdU729
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Layout"
msgstr "crwdns5523:0crwdne5523:0"

#. js-lingui-id: haJ2Hb
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Less than"
msgstr "crwdns5191:0crwdne5191:0"

#. js-lingui-id: 1njn7W
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Light"
msgstr "crwdns3995:0crwdne3995:0"

#. js-lingui-id: pQjjYo
#: src/pages/onboarding/InviteTeam.tsx
#: src/modules/workspace/components/WorkspaceInviteLink.tsx
msgid "Link copied to clipboard"
msgstr "crwdns467:0crwdne467:0"

#. js-lingui-id: DL2sg0
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Listings"
msgstr "crwdns3997:0crwdne3997:0"

#. js-lingui-id: s1MstR
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Loading metrics data..."
msgstr "crwdns5491:0crwdne5491:0"

#. js-lingui-id: FgAxTj
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Log out"
msgstr "crwdns469:0crwdne469:0"

#. js-lingui-id: nOhz3x
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Logout"
msgstr "crwdns5608:0crwdne5608:0"

#. js-lingui-id: PTozs8
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Look up users and manage their workspace feature flags or impersonate them."
msgstr "crwdns5193:0crwdne5193:0"

#. js-lingui-id: GPSwzy
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Look up users to impersonate them."
msgstr "crwdns5195:0crwdne5195:0"

#. js-lingui-id: U8dG4j
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
msgid "Manage favorite"
msgstr "crwdns5197:0crwdne5197:0"

#. js-lingui-id: T6YjCk
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Manage Members"
msgstr "crwdns473:0crwdne473:0"

#. js-lingui-id: 4cjU2u
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Manage the members of your space here"
msgstr "crwdns475:0crwdne475:0"

#. js-lingui-id: FyFNsd
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Manage your internet accounts."
msgstr "crwdns477:0crwdne477:0"

#. js-lingui-id: 36kYu0
#: src/pages/settings/SettingsBilling.tsx
msgid "Manage your subscription"
msgstr "crwdns479:0crwdne479:0"

#. js-lingui-id: 3Sdni6
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuDropdown.stories.tsx
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuBarEntry.stories.tsx
msgid "Mark as done"
msgstr "crwdns481:0crwdne481:0"

#. js-lingui-id: CK1KXz
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Max"
msgstr "crwdns483:0crwdne483:0"

#. js-lingui-id: wlQNTg
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Members"
msgstr "crwdns485:0crwdne485:0"

#. js-lingui-id: elpx6r
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Message Sync"
msgstr "crwdns5551:0crwdne5551:0"

#. js-lingui-id: Lt5rXo
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
#~ msgid "Message Sync Status"
#~ msgstr "Message Sync Status"

#. js-lingui-id: 6GBt0m
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Metadata"
msgstr "crwdns5199:0crwdne5199:0"

#. js-lingui-id: dN5YOb
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Metadata file generation failed"
msgstr "crwdns4935:0crwdne4935:0"

#. js-lingui-id: eTUF28
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Min"
msgstr "crwdns487:0crwdne487:0"

#. js-lingui-id: 8g3Cgz
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Monitor the execution of your calendar events sync job"
msgstr "crwdns5553:0crwdne5553:0"

#. js-lingui-id: IL/nIb
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Monitor the execution of your emails sync job"
msgstr "crwdns5555:0crwdne5555:0"

#. js-lingui-id: 3Siwmw
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "More options"
msgstr "crwdns489:0crwdne489:0"

#. js-lingui-id: iSLA/r
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
msgid "Move left"
msgstr "crwdns491:0crwdne491:0"

#. js-lingui-id: Ubl2by
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
msgid "Move right"
msgstr "crwdns493:0crwdne493:0"

#. js-lingui-id: 6YtxFj
#: src/testing/mock-data/tableData.ts
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/workspace/components/NameField.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissionsSettingsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissionsObjectsTableHeader.tsx
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentTableHeader.tsx
#: src/modules/settings/roles/components/RolesTableHeader.tsx
#: src/modules/settings/developers/components/SettingsApiKeysTable.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Name"
msgstr "crwdns495:0crwdne495:0"

#. js-lingui-id: XSwyCU
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Name can not be empty"
msgstr "crwdns497:0crwdne497:0"

#. js-lingui-id: zaxmAs
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Name in both singular (e.g., 'Invoice') and plural (e.g., 'Invoices') forms."
msgstr "crwdns3999:0crwdne3999:0"

#. js-lingui-id: z+6jaZ
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Name of your API key"
msgstr "crwdns499:0crwdne499:0"

#. js-lingui-id: J7w8lI
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Name of your workspace"
msgstr "crwdns501:0crwdne501:0"

#. js-lingui-id: 2T8KCk
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Navigate to next record"
msgstr "crwdns503:0crwdne503:0"

#. js-lingui-id: UX6+vb
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Navigate to next run"
msgstr "crwdns5405:0crwdne5405:0"

#. js-lingui-id: veSA19
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Navigate to next version"
msgstr "crwdns505:0crwdne505:0"

#. js-lingui-id: ZTEho+
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Navigate to next workflow"
msgstr "crwdns507:0crwdne507:0"

#. js-lingui-id: 2tw9bo
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Navigate to previous record"
msgstr "crwdns509:0crwdne509:0"

#. js-lingui-id: HddE65
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "Navigate to previous run"
msgstr "crwdns5407:0crwdne5407:0"

#. js-lingui-id: I+Pm5V
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Navigate to previous version"
msgstr "crwdns511:0crwdne511:0"

#. js-lingui-id: QVUN3K
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Navigate to previous workflow"
msgstr "crwdns513:0crwdne513:0"

#. js-lingui-id: isRobC
#: src/pages/settings/data-model/SettingsNewObject.tsx
msgid "New"
msgstr "crwdns515:0crwdne515:0"

#. js-lingui-id: Gntx7w
#: src/modules/command-menu/hooks/useOpenRecordInCommandMenu.ts
msgid "New {capitalizedObjectNameSingular}"
msgstr "crwdns4853:0{capitalizedObjectNameSingular}crwdne4853:0"

#. js-lingui-id: Kcr9Fr
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "New account"
msgstr "crwdns517:0crwdne517:0"

#. js-lingui-id: 2qr/61
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "New Approved Access Domain"
msgstr "crwdns4937:0crwdne4937:0"

#. js-lingui-id: 8YPqRx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
msgid "New Field"
msgstr "crwdns4001:0crwdne4001:0"

#. js-lingui-id: o8MyXb
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "New key"
msgstr "crwdns519:0crwdne519:0"

#. js-lingui-id: j313SZ
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "New Key"
msgstr "crwdns521:0crwdne521:0"

#. js-lingui-id: hFxdey
#: src/pages/settings/data-model/SettingsNewObject.tsx
msgid "New Object"
msgstr "crwdns523:0crwdne523:0"

#. js-lingui-id: 7vhWI8
#: src/pages/auth/PasswordReset.tsx
msgid "New Password"
msgstr "crwdns525:0crwdne525:0"

#. js-lingui-id: BcCzLv
#: src/modules/ui/layout/page/components/PageAddButton.tsx
#: src/modules/ui/layout/page/components/PageAddButton.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "New record"
msgstr "crwdns527:0crwdne527:0"

#. js-lingui-id: 2lmOC5
#: src/pages/settings/roles/SettingsRoles.tsx
#~ msgid "New Role"
#~ msgstr "New Role"

#. js-lingui-id: OzWuT+
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
msgid "New SSO Configuration"
msgstr "crwdns5201:0crwdne5201:0"

#. js-lingui-id: C7WtCv
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
msgid "New SSO provider"
msgstr "crwdns4939:0crwdne4939:0"

#. js-lingui-id: U1DAok
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhooksNew.tsx
#~ msgid "New Webhook"
#~ msgstr "New Webhook"

#. js-lingui-id: OTe3RI
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "No change detected"
msgstr "crwdns5351:0crwdne5351:0"

#. js-lingui-id: pwenQu
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "No connected account"
msgstr "crwdns531:0crwdne531:0"

#. js-lingui-id: 4BSfjK
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "No country"
msgstr "crwdns5203:0crwdne5203:0"

#. js-lingui-id: UwvrGq
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "No members"
msgstr "crwdns5572:0crwdne5572:0"

#. js-lingui-id: Y4qK8/
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "No members assigned"
msgstr "crwdns5574:0crwdne5574:0"

#. js-lingui-id: hfYSED
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "No members assigned to this role yet"
#~ msgstr "No members assigned to this role yet"

#. js-lingui-id: /nLvVj
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "No members match your search"
msgstr "crwdns5576:0crwdne5576:0"

#. js-lingui-id: F9pWel
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No members matching this search"
#~ msgstr "No members matching this search"

#. js-lingui-id: NluSN3
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "No members matching your search"
#~ msgstr "No members matching your search"

#. js-lingui-id: 0NudpV
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "No metrics data available"
msgstr "crwdns5495:0crwdne5495:0"

#. js-lingui-id: iMCnTm
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No more members to add"
#~ msgstr "No more members to add"

#. js-lingui-id: DL8pzn
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "No more members to assign"
msgstr "crwdns4839:0crwdne4839:0"

#. js-lingui-id: EqGTpW
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
msgid "No records found"
msgstr "crwdns4941:0crwdne4941:0"

#. js-lingui-id: 4bobEy
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No Result"
#~ msgstr "No Result"

#. js-lingui-id: MA3x23
#: src/modules/settings/roles/role-assignment/components/RoleAssignmentWorkspaceMemberPickerDropdownContent.tsx
msgid "No Results"
msgstr "crwdns4857:0crwdne4857:0"

#. js-lingui-id: Hsl+kr
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "No Select field"
msgstr "crwdns5205:0crwdne5205:0"

#. js-lingui-id: 0uWxPM
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "No workflow runs yet"
msgstr "crwdns533:0crwdne533:0"

#. js-lingui-id: AQCvCC
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "No workflow versions yet"
msgstr "crwdns535:0crwdne535:0"

#. js-lingui-id: EdQY6l
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
msgid "None"
msgstr "crwdns5207:0crwdne5207:0"

#. js-lingui-id: 1IipHp
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Norwegian"
msgstr "crwdns1825:0crwdne1825:0"

#. js-lingui-id: 0qBE9S
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Not available on Default View"
msgstr "crwdns5209:0crwdne5209:0"

#. js-lingui-id: 4wUkDk
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Not empty"
msgstr "crwdns537:0crwdne537:0"

#. js-lingui-id: pAtylB
#: src/modules/object-record/record-field/form-types/components/VariableChip.tsx
msgid "Not Found"
msgstr "crwdns5515:0crwdne5515:0"

#. js-lingui-id: hZWthZ
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Not synced"
msgstr "crwdns5211:0crwdne5211:0"

#. js-lingui-id: HptUxX
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowFormFieldSettingsNumber.tsx
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Number"
msgstr "crwdns5213:0crwdne5213:0"

#. js-lingui-id: 0fRFSb
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Number of decimals"
msgstr "crwdns5215:0crwdne5215:0"

#. js-lingui-id: qg5nhQ
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Number type"
msgstr "crwdns5217:0crwdne5217:0"

#. js-lingui-id: W0i24j
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Object"
msgstr "crwdns539:0crwdne539:0"

#. js-lingui-id: Zrauom
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Object destination"
msgstr "crwdns5219:0crwdne5219:0"

#. js-lingui-id: B3toQF
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissions.tsx
msgid "Objects"
msgstr "crwdns541:0crwdne541:0"

#. js-lingui-id: KNz3EF
#: src/pages/not-found/NotFound.tsx
msgid "Off the beaten path"
msgstr "crwdns543:0crwdne543:0"

#. js-lingui-id: zii2Qj
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
msgid "Only date & participants will be shared with your team."
msgstr "crwdns5221:0crwdne5221:0"

#. js-lingui-id: 50ETCF
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Only the timestamp & participants will be shared with your team."
msgstr "crwdns5223:0crwdne5223:0"

#. js-lingui-id: 69b7aE
#: src/modules/ui/layout/page-header/components/PageHeaderOpenCommandMenuButton.tsx
msgid "Open command menu"
msgstr "crwdns4861:0crwdne4861:0"

#. js-lingui-id: C39K59
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Open in"
msgstr "crwdns4943:0crwdne4943:0"

#. js-lingui-id: OV5wZZ
#: src/modules/object-metadata/components/NavigationDrawerOpenedSection.tsx
msgid "Opened"
msgstr "crwdns545:0crwdne545:0"

#. js-lingui-id: qNELak
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Optional: Define a secret string that we will include in every webhook. Use this to authenticate and verify the webhook upon receipt."
msgstr "crwdns5225:0crwdne5225:0"

#. js-lingui-id: 0zpgxV
#: src/modules/settings/roles/components/RolesDefaultRole.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdown.tsx
msgid "Options"
msgstr "crwdns547:0crwdne547:0"

#. js-lingui-id: BzEFor
#: src/pages/onboarding/InviteTeam.tsx
msgid "or"
msgstr "crwdns549:0crwdne549:0"

#. js-lingui-id: /IX/7x
#: src/pages/settings/Releases.tsx
#: src/pages/settings/lab/SettingsLab.tsx
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Other"
msgstr "crwdns551:0crwdne551:0"

#. js-lingui-id: 3V8SRM
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
msgid "Other Environment Variables"
msgstr "crwdns5497:0crwdne5497:0"

#. js-lingui-id: P7KMWx
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
msgid "Other Variables"
msgstr "crwdns5499:0crwdne5499:0"

#. js-lingui-id: +vDFPm
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownWorkspacesListComponents.tsx
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Other workspaces"
msgstr "crwdns5567:0crwdne5567:0"

#. js-lingui-id: boJlGf
#: src/pages/not-found/NotFound.tsx
msgid "Page Not Found"
msgstr "crwdns553:0crwdne553:0"

#. js-lingui-id: 8ZsakT
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Password"
msgstr "crwdns555:0crwdne555:0"

#. js-lingui-id: BxQ79w
#: src/pages/auth/PasswordReset.tsx
msgid "Password has been updated"
msgstr "crwdns557:0crwdne557:0"

#. js-lingui-id: mi6Rel
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
msgid "Password reset link has been sent to the email"
msgstr "crwdns559:0crwdne559:0"

#. js-lingui-id: PxBA+g
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "People I’ve sent emails to and received emails from."
msgstr "crwdns5227:0crwdne5227:0"

#. js-lingui-id: U/UvMm
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "People I’ve sent emails to."
msgstr "crwdns5229:0crwdne5229:0"

#. js-lingui-id: SrVzRe
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "Percent"
msgstr "crwdns561:0crwdne561:0"

#. js-lingui-id: yIK1GU
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Percent empty"
msgstr "crwdns563:0crwdne563:0"

#. js-lingui-id: PWLd4c
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Percent not empty"
msgstr "crwdns565:0crwdne565:0"

#. js-lingui-id: /roQKz
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Percentage"
msgstr "crwdns5231:0crwdne5231:0"

#. js-lingui-id: Bv3y5w
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Permanently destroy record"
msgstr "crwdns567:0crwdne567:0"

#. js-lingui-id: xjWlSJ
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Permanently destroy records"
msgstr "crwdns569:0crwdne569:0"

#. js-lingui-id: uKWXhB
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Permanently destroy workflows"
msgstr "crwdns571:0crwdne571:0"

#. js-lingui-id: 9cDpsw
#: src/pages/settings/roles/SettingsRoleEdit.tsx
msgid "Permissions"
msgstr "crwdns573:0crwdne573:0"

#. js-lingui-id: qlqT9z
#: src/modules/object-record/record-field/meta-types/display/components/PhonesFieldDisplay.tsx
msgid "Phone number copied to clipboard"
msgstr "crwdns4945:0crwdne4945:0"

#. js-lingui-id: N0+GsR
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsProfile.tsx
msgid "Picture"
msgstr "crwdns575:0crwdne575:0"

#. js-lingui-id: 0LrFTO
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Playground"
msgstr "crwdns5453:0crwdne5453:0"

#. js-lingui-id: jEw0Mr
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Please enter a valid URL"
msgstr "crwdns577:0crwdne577:0"

#. js-lingui-id: X5x85V
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Please search for a user first"
msgstr "crwdns4947:0crwdne4947:0"

#. js-lingui-id: 6nsIo3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Please type \"{confirmationValue}\" to confirm you want to delete this API Key. Be aware that any script using this key will stop working."
msgstr "crwdns579:0{confirmationValue}crwdne579:0"

#. js-lingui-id: mFZTXr
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Please type {confirmationText} to confirm you want to delete this webhook."
msgstr "crwdns581:0{confirmationText}crwdne581:0"

#. js-lingui-id: aRWD63
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Please use different names for your source and destination fields"
msgstr "crwdns5541:0crwdne5541:0"

#. js-lingui-id: BPig2P
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Plural"
msgstr "crwdns4003:0crwdne4003:0"

#. js-lingui-id: trnWaw
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Polish"
msgstr "crwdns1827:0crwdne1827:0"

#. js-lingui-id: MOERNx
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese"
#~ msgstr "Portuguese"

#. js-lingui-id: 0nsqwk
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Portuguese — Brazil"
msgstr "crwdns583:0crwdne583:0"

#. js-lingui-id: xtXHeo
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Portuguese — Portugal"
msgstr "crwdns585:0crwdne585:0"

#. js-lingui-id: R7+D0/
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portuguese (Brazil)"

#. js-lingui-id: 512Uma
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese (Portugal)"
#~ msgstr "Portuguese (Portugal)"

#. js-lingui-id: rdUucN
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectSettingsFormCard.tsx
#: src/modules/settings/data-model/components/SettingsDataModelPreviewFormCard.tsx
msgid "Preview"
msgstr "crwdns587:0crwdne587:0"

#. js-lingui-id: LcET2C
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "Privacy Policy"
msgstr "crwdns589:0crwdne589:0"

#. js-lingui-id: vERlcd
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Profile"
msgstr "crwdns591:0crwdne591:0"

#. js-lingui-id: GVxbU6
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Provide your OIDC provider details"
msgstr "crwdns4949:0crwdne4949:0"

#. js-lingui-id: YJgRqq
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Pseudo-English"
msgstr "crwdns593:0crwdne593:0"

#. js-lingui-id: /IUt/5
#: src/modules/settings/admin-panel/health-status/components/WorkerHealthStatus.tsx
msgid "Queue information is not available because the worker is down"
msgstr "crwdns5501:0crwdne5501:0"

#. js-lingui-id: e9OqcR
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Queue performance"
msgstr "crwdns5503:0crwdne5503:0"

#. js-lingui-id: ibPuCP
#: src/modules/settings/developers/components/SettingsReadDocumentationButton.tsx
#~ msgid "Read documentation"
#~ msgstr "Read documentation"

#. js-lingui-id: v3xM25
#: src/modules/settings/profile/components/ChangePassword.tsx
msgid "Receive an email containing password update link"
msgstr "crwdns597:0crwdne597:0"

#. js-lingui-id: ZVr1+K
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Recent Events (oldest → newest)"
msgstr "crwdns5505:0crwdne5505:0"

#. js-lingui-id: gcoiFh
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Reconnect"
msgstr "crwdns5233:0crwdne5233:0"

#. js-lingui-id: mj1fkT
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
msgid "Record image"
msgstr "crwdns5235:0crwdne5235:0"

#. js-lingui-id: K6/7kH
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
msgid "Record label"
msgstr "crwdns5237:0crwdne5237:0"

#. js-lingui-id: mAHjRd
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Record Page"
msgstr "crwdns5239:0crwdne5239:0"

#. js-lingui-id: dSCufP
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Record Selection"
msgstr "crwdns599:0crwdne599:0"

#. js-lingui-id: LfH+Ea
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Redirect Url copied to clipboard"
msgstr "crwdns4951:0crwdne4951:0"

#. js-lingui-id: RZjynQ
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Redirection URI"
msgstr "crwdns4953:0crwdne4953:0"

#. js-lingui-id: vpZcGd
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate an API key"
msgstr "crwdns601:0crwdne601:0"

#. js-lingui-id: Mwqo5m
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate key"
msgstr "crwdns603:0crwdne603:0"

#. js-lingui-id: D+Mv78
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate Key"
msgstr "crwdns605:0crwdne605:0"

#. js-lingui-id: UiAJoB
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Relation type"
msgstr "crwdns5241:0crwdne5241:0"

#. js-lingui-id: 5icoS1
#: src/pages/settings/Releases.tsx
#: src/pages/settings/Releases.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Releases"
msgstr "crwdns607:0crwdne607:0"

#. js-lingui-id: HpK/8d
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Reload"
msgstr "crwdns5409:0crwdne5409:0"

#. js-lingui-id: t/YqKh
#: src/modules/ui/input/components/ImageInput.tsx
msgid "Remove"
msgstr "crwdns609:0crwdne609:0"

#. js-lingui-id: ken+P9
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModal.tsx
#~ msgid "Remove {workspaceMemberName}?"
#~ msgstr "Remove {workspaceMemberName}?"

#. js-lingui-id: 1O32oy
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Remove account"
msgstr "crwdns5243:0crwdne5243:0"

#. js-lingui-id: Q2u5E9
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Remove as default"
msgstr "crwdns5245:0crwdne5245:0"

#. js-lingui-id: T/pF0Z
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Remove from favorites"
msgstr "crwdns611:0crwdne611:0"

#. js-lingui-id: 00Lxnh
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Remove option"
msgstr "crwdns5247:0crwdne5247:0"

#. js-lingui-id: OfhWJH
#: src/modules/views/components/ViewBarDetails.tsx
msgid "Reset"
msgstr "crwdns5249:0crwdne5249:0"

#. js-lingui-id: KbS2K9
#: src/pages/auth/PasswordReset.tsx
msgid "Reset Password"
msgstr "crwdns613:0crwdne613:0"

#. js-lingui-id: 1IWc1n
#: src/modules/command-menu/components/ResetContextToSelectionCommandButton.tsx
msgid "Reset to"
msgstr "crwdns615:0crwdne615:0"

#. js-lingui-id: WHiaOl
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "REST"
msgstr "crwdns5455:0crwdne5455:0"

#. js-lingui-id: yKu/3Y
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Restore"
msgstr "crwdns5417:0crwdne5417:0"

#. js-lingui-id: y4Ib1n
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Restore record"
msgstr "crwdns5419:0crwdne5419:0"

#. js-lingui-id: Vw369F
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "Restore records"
msgstr "crwdns5421:0crwdne5421:0"

#. js-lingui-id: kx0s+n
#: src/modules/command-menu/hooks/useCommandMenuSearchRecords.tsx
msgid "Results"
msgstr "crwdns617:0crwdne617:0"

#. js-lingui-id: 5dJK4M
#: src/pages/settings/roles/SettingsRoles.tsx
#: src/pages/settings/roles/SettingsRoles.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Roles"
msgstr "crwdns619:0crwdne619:0"

#. js-lingui-id: uJc01W
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Romanian"
msgstr "crwdns1831:0crwdne1831:0"

#. js-lingui-id: UX0N2y
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Run a workflow and return here to view its executions"
msgstr "crwdns621:0crwdne621:0"

#. js-lingui-id: nji0/X
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Russian"
msgstr "crwdns1833:0crwdne1833:0"

#. js-lingui-id: tfDRzk
#: src/modules/settings/components/SaveAndCancelButtons/SaveButton.tsx
msgid "Save"
msgstr "crwdns5251:0crwdne5251:0"

#. js-lingui-id: apLRCm
#: src/modules/views/components/UpdateViewButtonGroup.tsx
msgid "Save as new view"
msgstr "crwdns5253:0crwdne5253:0"

#. js-lingui-id: QJ8HBJ
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Schema"
msgstr "crwdns5457:0crwdne5457:0"

#. js-lingui-id: A1taO8
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownWorkspacesListComponents.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#: src/modules/navigation/components/MainNavigationDrawerItems.tsx
#: src/modules/command-menu/hooks/useOpenRecordsSearchPageInCommandMenu.ts
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.ts
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.ts
msgid "Search"
msgstr "crwdns623:0crwdne623:0"

#. js-lingui-id: 8NBMeZ
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Search ''{commandMenuSearch}'' with..."
msgstr "crwdns625:0{commandMenuSearch}crwdne625:0"

#. js-lingui-id: l1/uy2
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Search a field..."
msgstr "crwdns627:0crwdne627:0"

#. js-lingui-id: t3n1Qy
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
#~ msgid "Search a member"
#~ msgstr "Search a member"

#. js-lingui-id: xdl79x
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Search a team member..."
msgstr "crwdns5578:0crwdne5578:0"

#. js-lingui-id: lnDfeK
#: src/modules/settings/data-model/fields/forms/components/SettingsObjectNewFieldSelector.tsx
msgid "Search a type"
msgstr "crwdns5255:0crwdne5255:0"

#. js-lingui-id: x55IVv
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "Search an assigned team member..."
msgstr "crwdns5580:0crwdne5580:0"

#. js-lingui-id: k7kp5/
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
msgid "Search an index..."
msgstr "crwdns629:0crwdne629:0"

#. js-lingui-id: 7taA9j
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownFilterSelect.tsx
#: src/modules/object-record/advanced-filter/components/AdvancedFilterFieldSelectSearchInput.tsx
msgid "Search fields"
msgstr "crwdns631:0crwdne631:0"

#. js-lingui-id: ofuw3g
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Search for an object..."
msgstr "crwdns633:0crwdne633:0"

#. js-lingui-id: lnwW17
#: src/modules/ui/input/components/IconPicker.tsx
msgid "Search icon"
msgstr "crwdns5257:0crwdne5257:0"

#. js-lingui-id: IMeaSJ
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.ts
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.ts
msgid "Search records"
msgstr "crwdns635:0crwdne635:0"

#. js-lingui-id: 8VEDbV
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Secret"
msgstr "crwdns5259:0crwdne5259:0"

#. js-lingui-id: a3LDKx
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Security"
msgstr "crwdns637:0crwdne637:0"

#. js-lingui-id: QREcJS
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "See active version"
msgstr "crwdns639:0crwdne639:0"

#. js-lingui-id: BxIUpp
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "See deleted records"
msgstr "crwdns5377:0crwdne5377:0"

#. js-lingui-id: Z+SGGW
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "See deleted runs"
msgstr "crwdns5411:0crwdne5411:0"

#. js-lingui-id: 91fiCe
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "See deleted versions"
msgstr "crwdns5413:0crwdne5413:0"

#. js-lingui-id: 5jZIe5
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "See deleted workflows"
msgstr "crwdns5415:0crwdne5415:0"

#. js-lingui-id: OpPn5Z
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "See runs"
msgstr "crwdns641:0crwdne641:0"

#. js-lingui-id: gGEfj/
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "See version"
msgstr "crwdns5610:0crwdne5610:0"

#. js-lingui-id: EtyY4+
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "See versions"
msgstr "crwdns643:0crwdne643:0"

#. js-lingui-id: lYhPN0
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "See versions history"
msgstr "crwdns645:0crwdne645:0"

#. js-lingui-id: qEKLJV
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
msgid "See workflow"
msgstr "crwdns5612:0crwdne5612:0"

#. js-lingui-id: XThiR2
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
msgid "See workflows"
msgstr "crwdns5614:0crwdne5614:0"

#. js-lingui-id: WZvt/6
#: src/modules/command-menu/hooks/useWorkflowCommandMenu.ts
#: src/modules/command-menu/hooks/__tests__/useWorkflowCommandMenu.test.tsx
msgid "Select Action"
msgstr "crwdns5461:0crwdne5461:0"

#. js-lingui-id: pofGCP
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
msgid "Select the default value for this boolean field"
msgstr "crwdns5261:0crwdne5261:0"

#. js-lingui-id: xraglu
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Select the events you wish to send to this endpoint"
msgstr "crwdns647:0crwdne647:0"

#. js-lingui-id: AXTJAW
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Select your preferred language"
msgstr "crwdns649:0crwdne649:0"

#. js-lingui-id: mjK8F3
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Send an invite email to your team"
msgstr "crwdns651:0crwdne651:0"

#. js-lingui-id: h69WC6
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Sent"
msgstr "crwdns5263:0crwdne5263:0"

#. js-lingui-id: hdVMzO
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Sent and Received"
msgstr "crwdns5265:0crwdne5265:0"

#. js-lingui-id: 6oxz/y
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Serbian (Cyrillic)"
msgstr "crwdns1837:0crwdne1837:0"

#. js-lingui-id: WKimFU
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#~ msgid "Server Admin"
#~ msgstr "Server Admin"

#. js-lingui-id: yy5k7a
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#~ msgid "Server Admin Panel"
#~ msgstr "Server Admin Panel"

#. js-lingui-id: LUc0oL
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Service Provider Details"
msgstr "crwdns4955:0crwdne4955:0"

#. js-lingui-id: YZwx1e
#: src/modules/settings/roles/components/RolesDefaultRole.tsx
msgid "Set a default role for this workspace"
msgstr "crwdns5357:0crwdne5357:0"

#. js-lingui-id: PPcets
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Set as default"
msgstr "crwdns5267:0crwdne5267:0"

#. js-lingui-id: V7fgiB
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Set email visibility, manage your blocklist and more."
msgstr "crwdns655:0crwdne655:0"

#. js-lingui-id: qNbuWB
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Set the name of your custom domain and configure your DNS records."
msgstr "crwdns4849:0crwdne4849:0"

#. js-lingui-id: cx14rp
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
#~ msgid "Set the name of your domain"
#~ msgstr "Set the name of your domain"

#. js-lingui-id: tn41zE
#: src/pages/settings/workspace/SettingsSubdomain.tsx
msgid "Set the name of your subdomain"
msgstr "crwdns659:0crwdne659:0"

#. js-lingui-id: Tz0i8g
#: src/pages/settings/roles/SettingsRoleEdit.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/roles/role-permissions/components/RolePermissions.tsx
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
#: src/modules/navigation/components/MainNavigationDrawerItems.tsx
msgid "Settings"
msgstr "crwdns661:0crwdne661:0"

#. js-lingui-id: p8fNBm
#: src/modules/settings/roles/role-permissions/components/RolePermissions.tsx
msgid "Settings permissions"
msgstr "crwdns4843:0crwdne4843:0"

#. js-lingui-id: Vy9kmk
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Share this link to invite users to join your workspace"
msgstr "crwdns663:0crwdne663:0"

#. js-lingui-id: gWk8gY
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Should changing a field's label also change the API name?"
msgstr "crwdns665:0crwdne665:0"

#. js-lingui-id: WFtdWr
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Should changing an object's label also change the API?"
msgstr "crwdns4007:0crwdne4007:0"

#. js-lingui-id: uVg8dY
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Side Panel"
msgstr "crwdns5269:0crwdne5269:0"

#. js-lingui-id: 5lWFkC
#: src/modules/auth/sign-in-up/components/SignInUpWithCredentials.tsx
msgid "Sign in"
msgstr "crwdns667:0crwdne667:0"

#. js-lingui-id: e+RpCP
#: src/modules/auth/sign-in-up/components/SignInUpWithCredentials.tsx
msgid "Sign up"
msgstr "crwdns669:0crwdne669:0"

#. js-lingui-id: 5v3IHX
#: src/modules/auth/sign-in-up/components/SignInUpWithSSO.tsx
msgid "Single sign-on (SSO)"
msgstr "crwdns671:0crwdne671:0"

#. js-lingui-id: maCaRp
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Singular"
msgstr "crwdns5271:0crwdne5271:0"

#. js-lingui-id: djfBXF
#: src/modules/settings/data-model/validation-schemas/settingsDataModelObjectAboutFormSchema.ts
msgid "Singular and plural labels must be different"
msgstr "crwdns5431:0crwdne5431:0"

#. js-lingui-id: zvwLTy
#: src/modules/settings/data-model/validation-schemas/settingsDataModelObjectAboutFormSchema.ts
msgid "Singular and plural names must be different"
msgstr "crwdns5433:0crwdne5433:0"

#. js-lingui-id: 6Uau97
#: src/pages/onboarding/InviteTeam.tsx
msgid "Skip"
msgstr "crwdns673:0crwdne673:0"

#. js-lingui-id: f6Hub0
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Sort"
msgstr "crwdns675:0crwdne675:0"

#. js-lingui-id: 65A04M
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Spanish"
msgstr "crwdns677:0crwdne677:0"

#. js-lingui-id: vnS6Rf
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "SSO"
msgstr "crwdns679:0crwdne679:0"

#. js-lingui-id: vlvAkg
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "SSO (SAML / OIDC)"
msgstr "crwdns5616:0crwdne5616:0"

#. js-lingui-id: ErU1td
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "Stages"
msgstr "crwdns5273:0crwdne5273:0"

#. js-lingui-id: uAQUqI
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Status"
msgstr "crwdns5275:0crwdne5275:0"

#. js-lingui-id: ku9TbG
#: src/pages/settings/workspace/SettingsSubdomain.tsx
msgid "Subdomain"
msgstr "crwdns681:0crwdne681:0"

#. js-lingui-id: omhc+7
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain already taken"
msgstr "crwdns683:0crwdne683:0"

#. js-lingui-id: OlC/tU
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain can not be longer than 30 characters"
msgstr "crwdns685:0crwdne685:0"

#. js-lingui-id: ZETwlU
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain can not be shorter than 3 characters"
msgstr "crwdns687:0crwdne687:0"

#. js-lingui-id: DTG2nE
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain updated"
msgstr "crwdns5363:0crwdne5363:0"

#. js-lingui-id: 97eDN2
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Subject and metadata"
msgstr "crwdns5277:0crwdne5277:0"

#. js-lingui-id: LYbP/A
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Subject and metadata will be shared with your team."
msgstr "crwdns5279:0crwdne5279:0"

#. js-lingui-id: J9ylmk
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Subject, body and attachments will be shared with your team."
msgstr "crwdns5281:0crwdne5281:0"

#. js-lingui-id: B5jRKH
#: src/pages/settings/SettingsBilling.tsx
msgid "Subscription has been switched {to}"
msgstr "crwdns689:0{to}crwdne689:0"

#. js-lingui-id: AxQiPW
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Sum"
msgstr "crwdns691:0crwdne691:0"

#. js-lingui-id: XYLcNv
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Support"
msgstr "crwdns693:0crwdne693:0"

#. js-lingui-id: UaISq3
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Swedish"
msgstr "crwdns1839:0crwdne1839:0"

#. js-lingui-id: 9yk9d1
#: src/pages/settings/SettingsBilling.tsx
msgid "Switch {from}"
msgstr "crwdns695:0{from}crwdne695:0"

#. js-lingui-id: qi74XZ
#: src/pages/settings/SettingsBilling.tsx
msgid "Switch {to}"
msgstr "crwdns697:0{to}crwdne697:0"

#. js-lingui-id: L6Fg36
#: src/pages/settings/SettingsBilling.tsx
msgid "Switch billing {to}"
msgstr "crwdns699:0{to}crwdne699:0"

#. js-lingui-id: lz+f/E
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Switch to {alternatePlanName}"
msgstr "crwdns5618:0{alternatePlanName}crwdne5618:0"

#. js-lingui-id: 5TRY4+
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Sync failed"
msgstr "crwdns5283:0crwdne5283:0"

#. js-lingui-id: N2FcBE
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Synced"
msgstr "crwdns5285:0crwdne5285:0"

#. js-lingui-id: AtzMpB
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Synchronize Field Label and API Name"
msgstr "crwdns701:0crwdne701:0"

#. js-lingui-id: WZ6bN9
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Synchronize Objects Labels and API Names"
msgstr "crwdns4009:0crwdne4009:0"

#. js-lingui-id: D4SseJ
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
msgid "System settings"
msgstr "crwdns4011:0crwdne4011:0"

#. js-lingui-id: E3AMmw
#: src/pages/settings/profile/appearance/components/DateTimeSettingsDateFormatSelect.tsx
msgid "System settings - {systemDateFormatLabel}"
msgstr "crwdns703:0{systemDateFormatLabel}crwdne703:0"

#. js-lingui-id: 0ZgB1e
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "System Settings - {systemTimeFormatLabel}"
msgstr "crwdns705:0{systemTimeFormatLabel}crwdne705:0"

#. js-lingui-id: 4hJhzz
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "Table"
msgstr "crwdns5287:0crwdne5287:0"

#. js-lingui-id: xowcRf
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "Terms of Service"
msgstr "crwdns707:0crwdne707:0"

#. js-lingui-id: NnH3pK
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Test"
msgstr "crwdns709:0crwdne709:0"

#. js-lingui-id: bU9B27
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
msgid "Test Workflow"
msgstr "crwdns711:0crwdne711:0"

#. js-lingui-id: xeiujy
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowFormFieldSettingsNumber.tsx
msgid "Text"
msgstr "crwdns5588:0crwdne5588:0"

#. js-lingui-id: lyaiTc
#: src/modules/object-record/record-field/components/LightCopyIconButton.tsx
msgid "Text copied to clipboard"
msgstr "crwdns4957:0crwdne4957:0"

#. js-lingui-id: 1xQkU9
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "The default country code for new phone numbers."
msgstr "crwdns5289:0crwdne5289:0"

#. js-lingui-id: PmXLtL
#: src/modules/settings/data-model/fields/forms/address/components/SettingsDataModelFieldAddressForm.tsx
msgid "The default country for new addresses"
msgstr "crwdns5291:0crwdne5291:0"

#. js-lingui-id: 2OUtmv
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The description of this field"
msgstr "crwdns713:0crwdne713:0"

#. js-lingui-id: VGZYbZ
#: src/pages/settings/SettingsProfile.tsx
msgid "The email associated to your account"
msgstr "crwdns715:0crwdne715:0"

#. js-lingui-id: h8mvCd
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The name and icon of this field"
msgstr "crwdns717:0crwdne717:0"

#. js-lingui-id: e/dfFe
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "The name of your connection"
msgstr "crwdns5293:0crwdne5293:0"

#. js-lingui-id: 8EkdZh
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "The name of your Domain"
msgstr "crwdns5295:0crwdne5295:0"

#. js-lingui-id: +C8Rdp
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "The name of your organization"
msgstr "crwdns719:0crwdne719:0"

#. js-lingui-id: L97sPr
#: src/pages/not-found/NotFound.tsx
msgid "The page you're seeking is either gone or never was. Let's get you back on track"
msgstr "crwdns721:0crwdne721:0"

#. js-lingui-id: uWikAA
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The values of this field"
msgstr "crwdns723:0crwdne723:0"

#. js-lingui-id: +69KDk
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
msgid "The values of this field must be unique"
msgstr "crwdns725:0crwdne725:0"

#. js-lingui-id: MHLapp
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
msgid "The whole event details will be shared with your team."
msgstr "crwdns5297:0crwdne5297:0"

#. js-lingui-id: FEr96N
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownThemesComponents.tsx
msgid "Theme"
msgstr "crwdns5569:0crwdne5569:0"

#. js-lingui-id: 6tNbxl
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Theme "
msgstr "crwdns5571:0crwdne5571:0"

#. js-lingui-id: ynfkhb
#: src/pages/auth/PasswordReset.tsx
msgid "There was an error while updating password."
msgstr "crwdns727:0crwdne727:0"

#. js-lingui-id: PmtLRf
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
msgid "There was an issue"
msgstr "crwdns729:0crwdne729:0"

#. js-lingui-id: DHjmMm
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
msgid "These are only the server values. Ensure your worker environment has the same variables and values, this is required for asynchronous tasks like email sync."
msgstr "crwdns5537:0crwdne5537:0"

#. js-lingui-id: hqCwGc
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "This action cannot be undone. This will permanently delete this user and remove them from all their assignments."
msgstr "crwdns731:0crwdne731:0"

#. js-lingui-id: gWGuHC
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "This action cannot be undone. This will permanently delete your entire workspace. <0/> Please type in your email to confirm."
msgstr "crwdns733:0crwdne733:0"

#. js-lingui-id: yHIStW
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModalSubtitle.tsx
#~ msgid "This member will be unassigned from this role."
#~ msgstr "This member will be unassigned from this role."

#. js-lingui-id: /tr8Uy
#: src/pages/settings/roles/components/RolePermissions.tsx
#~ msgid "This Role has the following permissions."
#~ msgstr "This Role has the following permissions."

#. js-lingui-id: 6j5nJX
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "This Role is assigned to these workspace member."
#~ msgstr "This Role is assigned to these workspace member."

#. js-lingui-id: xPfDRx
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
msgid "This role is assigned to these workspace members."
msgstr "crwdns1843:0crwdne1843:0"

#. js-lingui-id: yByRxz
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "This week"
msgstr "crwdns5507:0crwdne5507:0"

#. js-lingui-id: n9nSNJ
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "Time format"
msgstr "crwdns739:0crwdne739:0"

#. js-lingui-id: Mz2JN2
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
msgid "Time zone"
msgstr "crwdns5299:0crwdne5299:0"

#. js-lingui-id: /VwdtK
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Timestamp and participants will be shared with your team."
msgstr "crwdns5301:0crwdne5301:0"

#. js-lingui-id: aqMzDX
#: src/pages/settings/SettingsBilling.tsx
msgid "to monthly"
msgstr "crwdns741:0crwdne741:0"

#. js-lingui-id: WXXiXO
#: src/pages/settings/SettingsBilling.tsx
msgid "to yearly"
msgstr "crwdns743:0crwdne743:0"

#. js-lingui-id: ecUA8p
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Today"
msgstr "crwdns5509:0crwdne5509:0"

#. js-lingui-id: vb0Q0/
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#~ msgid "Total Users"
#~ msgstr "Total Users"

#. js-lingui-id: PiUt3N
#: src/modules/command-menu/hooks/useWorkflowCommandMenu.ts
#: src/modules/command-menu/hooks/__tests__/useWorkflowCommandMenu.test.tsx
msgid "Trigger Type"
msgstr "crwdns745:0crwdne745:0"

#. js-lingui-id: IQ5pAL
#: src/modules/workflow/components/RecordShowPageWorkflowHeader.tsx
#~ msgid "Trigger type should be Manual - when no record(s) are selected"
#~ msgstr "Trigger type should be Manual - when no record(s) are selected"

#. js-lingui-id: c+xCSz
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "True"
msgstr "crwdns5305:0crwdne5305:0"

#. js-lingui-id: haaL9N
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Try our REST or GraphQL API playgrounds."
msgstr "crwdns5459:0crwdne5459:0"

#. js-lingui-id: Kz91g/
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Turkish"
msgstr "crwdns1845:0crwdne1845:0"

#. js-lingui-id: +zy2Nq
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "Type"
msgstr "crwdns749:0crwdne749:0"

#. js-lingui-id: U83IeL
#: src/modules/command-menu/components/CommandMenuTopBar.tsx
msgid "Type anything"
msgstr "crwdns751:0crwdne751:0"

#. js-lingui-id: V9+2pH
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Ukrainian"
msgstr "crwdns1847:0crwdne1847:0"

#. js-lingui-id: wSXm5S
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Unique"
msgstr "crwdns753:0crwdne753:0"

#. js-lingui-id: 29VNqC
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Unknown error"
msgstr "crwdns755:0crwdne755:0"

#. js-lingui-id: GQCXQS
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Unlimited contacts"
msgstr "crwdns757:0crwdne757:0"

#. js-lingui-id: +b7T3G
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Updated"
msgstr "crwdns5307:0crwdne5307:0"

#. js-lingui-id: ONWvwQ
#: src/modules/ui/input/components/ImageInput.tsx
msgid "Upload"
msgstr "crwdns759:0crwdne759:0"

#. js-lingui-id: 2IXDgU
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Upload file"
msgstr "crwdns4959:0crwdne4959:0"

#. js-lingui-id: akDOEO
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Upload the XML file with your connection infos"
msgstr "crwdns4961:0crwdne4961:0"

#. js-lingui-id: IagCbF
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "URL"
msgstr "crwdns761:0crwdne761:0"

#. js-lingui-id: 6dMpmz
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
msgid "Use as draft"
msgstr "crwdns763:0crwdne763:0"

#. js-lingui-id: oTTQsc
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Use letter, number and dash only. Start and finish with a letter or a number"
msgstr "crwdns765:0crwdne765:0"

#. js-lingui-id: c6uZUV
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Use our API or add your first {objectLabel} manually"
msgstr "crwdns767:0{objectLabel}crwdne767:0"

#. js-lingui-id: 7PzzBU
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/pages/settings/accounts/SettingsAccounts.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "User"
msgstr "crwdns769:0crwdne769:0"

#. js-lingui-id: YFciqL
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User Email"
#~ msgstr "User Email"

#. js-lingui-id: GjhOGB
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User ID"
#~ msgstr "User ID"

#. js-lingui-id: RNv3YS
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "User Impersonation"
msgstr "crwdns5313:0crwdne5313:0"

#. js-lingui-id: tNT8wT
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "User Info"
msgstr "crwdns5315:0crwdne5315:0"

#. js-lingui-id: IjyOjp
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "User is not logged in"
msgstr "crwdns771:0crwdne771:0"

#. js-lingui-id: 5ZYU8G
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User Name"
#~ msgstr "User Name"

#. js-lingui-id: Sxm8rQ
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#~ msgid "Users"
#~ msgstr "Users"

#. js-lingui-id: fXVIZq
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Values"
msgstr "crwdns773:0crwdne773:0"

#. js-lingui-id: dY/1ir
#: src/modules/object-record/record-field/form-types/components/VariableChip.tsx
msgid "Variable not found"
msgstr "crwdns5517:0crwdne5517:0"

#. js-lingui-id: IHIWR4
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Version of the application"
msgstr "crwdns5321:0crwdne5321:0"

#. js-lingui-id: fROFIL
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Vietnamese"
msgstr "crwdns1849:0crwdne1849:0"

#. js-lingui-id: jpctdh
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
msgid "View"
msgstr "crwdns5323:0crwdne5323:0"

#. js-lingui-id: KANz0G
#: src/pages/settings/SettingsBilling.tsx
msgid "View billing details"
msgstr "crwdns775:0crwdne775:0"

#. js-lingui-id: igR+P/
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
msgid "View execution details"
msgstr "crwdns4963:0crwdne4963:0"

#. js-lingui-id: qZmd6a
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownViewSettingsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#~ msgid "View settings"
#~ msgstr "View settings"

#. js-lingui-id: bJAIqT
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "View type"
msgstr "crwdns5327:0crwdne5327:0"

#. js-lingui-id: 2q/Q7x
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Visibility"
msgstr "crwdns5329:0crwdne5329:0"

#. js-lingui-id: oh8+os
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Visible"
msgstr "crwdns5331:0crwdne5331:0"

#. js-lingui-id: JiDDG4
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Visible groups"
msgstr "crwdns5333:0crwdne5333:0"

#. js-lingui-id: 6n7jtr
#: src/modules/settings/data-model/objects/components/SettingsObjectCoverImage.tsx
msgid "Visualize"
msgstr "crwdns777:0crwdne777:0"

#. js-lingui-id: id6ein
#: src/modules/ui/input/components/ImageInput.tsx
msgid "We support your square PNGs, JPEGs and GIFs under 10MB"
msgstr "crwdns779:0crwdne779:0"

#. js-lingui-id: ZS7vYp
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "We will send POST requests to this endpoint for every new event"
msgstr "crwdns781:0crwdne781:0"

#. js-lingui-id: PI4LiB
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "We will send your a link to verify domain ownership"
msgstr "crwdns5335:0crwdne5335:0"

#. js-lingui-id: TRDppN
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Webhook"
msgstr "crwdns783:0crwdne783:0"

#. js-lingui-id: v1kQyJ
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Webhooks"
msgstr "crwdns785:0crwdne785:0"

#. js-lingui-id: Jt1WNL
#: src/pages/auth/SignInUp.tsx
#~ msgid "Welcome to"
#~ msgstr "Welcome to"

#. js-lingui-id: ke0EDP
#: src/pages/auth/SignInUp.tsx
msgid "Welcome to {workspaceName}"
msgstr "crwdns787:0{workspaceName}crwdne787:0"

#. js-lingui-id: C51ilI
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "When the API key will expire."
msgstr "crwdns789:0crwdne789:0"

#. js-lingui-id: leUubq
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "When the key will be disabled"
msgstr "crwdns791:0crwdne791:0"

#. js-lingui-id: wvyffT
#: src/modules/workflow/components/RecordShowPageWorkflowHeader.tsx
#~ msgid "Workflow cannot be tested"
#~ msgstr "Workflow cannot be tested"

#. js-lingui-id: GpJjC8
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
msgid "Workflow is running..."
msgstr "crwdns4967:0crwdne4967:0"

#. js-lingui-id: o0xBLi
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
msgid "Workflow run failed"
msgstr "crwdns4969:0crwdne4969:0"

#. js-lingui-id: pmUArF
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsBilling.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/pages/settings/roles/SettingsRoles.tsx
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/favorites/components/WorkspaceFavorites.tsx
msgid "Workspace"
msgstr "crwdns795:0crwdne795:0"

#. js-lingui-id: VicISP
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Workspace Deletion"
msgstr "crwdns797:0crwdne797:0"

#. js-lingui-id: J22jAC
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Workspace Info"
msgstr "crwdns5539:0crwdne5539:0"

#. js-lingui-id: Y0Fj4S
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Workspace logo"
msgstr "crwdns799:0crwdne799:0"

#. js-lingui-id: CozWO1
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Workspace name"
msgstr "crwdns801:0crwdne801:0"

#. js-lingui-id: 6X+cfX
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#~ msgid "Workspace Name"
#~ msgstr "Workspace Name"

#. js-lingui-id: pmt7u4
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Workspaces"
msgstr "crwdns5339:0crwdne5339:0"

#. js-lingui-id: 5iSD9O
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "Wrap on record pages"
msgstr "crwdns5341:0crwdne5341:0"

#. js-lingui-id: Q9pNST
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/modules/settings/roles/role-settings/components/RoleSettings.tsx
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldDescriptionForm.tsx
msgid "Write a description"
msgstr "crwdns803:0crwdne803:0"

#. js-lingui-id: L80fMJ
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
msgid "Write a secret"
msgstr "crwdns5343:0crwdne5343:0"

#. js-lingui-id: 3d1wCB
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "yes"
msgstr "crwdns805:0crwdne805:0"

#. js-lingui-id: MFDARJ
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
msgid "You are not allowed to create records for this object"
msgstr "crwdns5373:0crwdne5373:0"

#. js-lingui-id: 5eVYbs
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
#~ msgid "You are not allowed to create records in this object"
#~ msgstr "You are not allowed to create records in this object"

#. js-lingui-id: TBApzn
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "You do not have access to impersonate users."
#~ msgstr "You do not have access to impersonate users."

#. js-lingui-id: zSkMV0
#: src/pages/settings/SettingsBilling.tsx
msgid "You will be charged immediately for the full year."
msgstr "crwdns807:0crwdne807:0"

#. js-lingui-id: XVnj6K
#: src/pages/settings/SettingsBilling.tsx
msgid "Your credit balance will be used to pay the monthly bills."
msgstr "crwdns809:0crwdne809:0"

#. js-lingui-id: +5YqGH
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Your email subjects and meeting titles will be shared with your team."
msgstr "crwdns5345:0crwdne5345:0"

#. js-lingui-id: la0RPA
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Your emails and events content will be shared with your team."
msgstr "crwdns5347:0crwdne5347:0"

#. js-lingui-id: 9ivpwk
#: src/pages/settings/SettingsProfile.tsx
msgid "Your name as it will be displayed"
msgstr "crwdns811:0crwdne811:0"

#. js-lingui-id: 3RASGN
#: src/pages/onboarding/CreateProfile.tsx
msgid "Your name as it will be displayed on the app"
msgstr "crwdns813:0crwdne813:0"

#. js-lingui-id: YQK8fJ
#: src/pages/auth/SignInUp.tsx
msgid "Your Workspace"
msgstr "crwdns815:0crwdne815:0"

#. js-lingui-id: RhNbPE
#: src/pages/settings/SettingsBilling.tsx
msgid "Your workspace will be disabled"
msgstr "crwdns817:0crwdne817:0"
