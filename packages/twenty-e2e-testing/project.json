{"name": "twenty-e2e-testing", "projectType": "application", "tags": ["scope:testing"], "targets": {"setup": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-e2e-testing", "commands": ["yarn playwright install", "cp .env.example .env"]}}, "test": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-e2e-testing", "commands": ["yarn playwright test"]}}, "test:ui": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-e2e-testing", "commands": ["yarn playwright test --ui"]}}, "test:debug": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-e2e-testing", "commands": ["yarn playwright test --debug"]}}, "test:report": {"executor": "nx:run-commands", "options": {"cwd": "packages/twenty-e2e-testing", "commands": ["yarn playwright show-report"]}}}}