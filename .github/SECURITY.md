# Security Policy

## Reporting a Vulnerability

Reporting any potential vulnerabilities is strongly encouraged.

If you suspect a vulnerability, please take the following steps:
- Contact the team at `security at twenty.com`.
- Include a comprehensive description of the potential vulnerability and steps to reproduce the issue, if possible. The more information you can provide, the quicker Twenty can address the problem.

You can expect a response to your initial report within one business day.
While the core team works on addressing the issue, please maintain confidentiality about the vulnerability to ensure the security of all users.
Please refrain from exploiting the vulnerability or revealing the problem to others.

While Twenty doesn't have a formal bug bounty program right now due to the project's nascent stage, rest assured that:

- You will get a response within one business day.
- Your report and all accompanying data will receive the highest level of confidentiality.
- Your contribution is greatly appreciated, and Twenty would acknowledge your role in the vulnerability fix, if you opt for identification.
- Twenty will grant you permission to publicly discuss your findings once users have had a reasonable time to apply the patch after it becomes available.
- Twenty guarantees not to pursue any legal action as long as the vulnerability is not exploited.

## Security Features
Efforts are continually made to enhance the security of the product.
If you have any recommendations or feature request that could enhance the product's security, please share them via the discussion forum.

⚠️ Note this does not apply to security vulnerabilities. If you're in doubt, then always follow the security vulnerability process




