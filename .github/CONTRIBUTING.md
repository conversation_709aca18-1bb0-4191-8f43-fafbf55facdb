# Contributing to Twenty

Thanks for considering contributing to Twenty! 

Please make sure to go through the [documentation](https://docs.twenty.com) before. 

<br>


## Good first issues

Good first issues are a great way to start contributing and get familiar with the codebase. You can find them on by filtering on the [good first issue](https://github.com/twentyhq/twenty/labels/good%20first%20issue) label. 

## Issue assignment

To avoid conflicts, we follow these guidelines:

1. For `Good First Issue` and `Experienced Contributor` issues without `size: long` labels, we'll merge the first PRs that meet our [code quality standards](https://twenty.com/developers). **We don't assign contributors to these issues**. For `priority: high` issues, our core team will step in within days if no adequate contributions are received.
2. For `size: long` Issues, assigned contributors have one week to submit their first draft PR.

## How to Contribute

1. **Fork the Repository:** Click on the 'Fork' button in the upper right corner of the repository's GitHub page. This will create a copy of the repository in your GitHub account.

2. **Clone the Repository:** Clone your forked repository to your local machine using `git clone`.

```shell
git clone https://github.com/yourusername/twenty.git
cd twenty
```

3. **Create a New Branch:** Create a new branch for your changes instead of using the main branch.

```shell
git checkout -b your-branch-name
```

4. **Make Changes:** Make your desired changes and ensure that your code adheres to Twenty's coding standards.


5. **Test Locally:** Test your changes locally to ensure they work as expected.


6. **Commit Changes:** Commit your changes with a clear and concise commit message.

```shell
git commit -m "Add your detailed description here"
```

7. **Push Changes:** Push your changes to your forked repository.

```shell
git push origin your-branch-name
```

8. **Create a Pull Request:** Go to the original Twenty repository and create a pull request. Please provide a detailed description of your changes. Submitting a PR means you agree to the CLA.

9. **Code Review:** Your pull request will undergo a code review.

10. **Merge:** Once approved, maintainers will merge your pull request into the main repository.



## Reporting Issues

If you face any issues or have suggestions, please feel free to (create an issue on Twenty's GitHub repository)[https://github.com/twentyhq/twenty/issues/new]. Please provide as much detail as possible.
