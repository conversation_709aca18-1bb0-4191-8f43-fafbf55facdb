name: 'Preview Environment Dispatch'

on:
  # Using pull_request_target instead of pull_request to have access to secrets for external contributors
  # Security note: This is safe because we're only using the repository-dispatch action with limited scope
  # and not checking out or running any code from the external contributor's PR
  pull_request_target:
    types: [opened, synchronize, reopened, labeled]
    paths:
      - packages/twenty-docker/**
      - packages/twenty-server/**
      - packages/twenty-front/**
      - .github/workflows/preview-env-dispatch.yaml
      - .github/workflows/preview-env-keepalive.yaml

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  trigger-preview:
    permissions:
      contents: write
      actions: write
      pull-requests: read
    if: github.event.action == 'opened' || github.event.action == 'synchronize' || github.event.action == 'reopened' || (github.event.action == 'labeled' && github.event.label.name == 'preview-app')
    timeout-minutes: 5
    runs-on: ubuntu-latest
    steps:
      - name: Trigger preview environment workflow
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          event-type: preview-environment
          client-payload: '{"pr_number": "${{ github.event.pull_request.number }}", "pr_head_sha": "${{ github.event.pull_request.head.sha }}", "repo_full_name": "${{ github.repository }}"}'
