name: CI Utils
on:
  # it's usually not recommended to use pull_request_target
  # but we consider it's safe here if we keep the same steps
  # see: https://securitylab.github.com/research/github-actions-preventing-pwn-requests/
  # and: https://github.com/facebook/react-native/pull/34370/files
  pull_request_target:
    types: [opened, synchronize, reopened, closed]
permissions:
  actions: write
  checks: write
  contents: write
  issues: write
  pull-requests: write
  statuses: write
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  # We don't cancel in-progress because this workflow is triggered on
  # pull_request_target, which means the ref can be the same accross two PRs.
  # cancel-in-progress: true

jobs:
  danger-js:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Utils / Run Danger.js
        run: cd packages/twenty-utils && npx nx danger:ci
        env:
          DANGER_GITHUB_API_TOKEN: ${{ github.token }}
  
  congratulate:
    timeout-minutes: 3
    runs-on: ubuntu-latest
    if: github.event.action == 'closed' && github.event.pull_request.merged == true
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Run congratulate-dangerfile.js
        run: cd packages/twenty-utils && npx nx danger:congratulate
        env:
          DANGER_GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
