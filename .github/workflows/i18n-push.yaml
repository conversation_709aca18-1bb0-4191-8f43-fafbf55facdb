name: 'Push translations to Crowdin'

on:
  workflow_dispatch:
  workflow_call:
  push:
    branches: ['main']

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  extract_translations:
    name: Extract and upload translations
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ github.token }}
          ref: main

      - name: Setup i18n branch
        run: |
          git fetch origin i18n || true
          git checkout -B i18n origin/i18n || git checkout -b i18n

      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install

      - name: Build dependencies
        run: npx nx build twenty-shared

      - name: Extract translations
        run: |
          npx nx run twenty-server:lingui:extract
          npx nx run twenty-emails:lingui:extract
          npx nx run twenty-front:lingui:extract

      - name: Check and commit extracted files
        id: check_extract_changes
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'
          git add .
          if ! git diff --staged --quiet --exit-code; then
            git commit -m "chore: extract translations"
            echo "changes_detected=true" >> $GITHUB_OUTPUT
          else
            echo "changes_detected=false" >> $GITHUB_OUTPUT
          fi

      - name: Compile translations
        run: |
          npx nx run twenty-server:lingui:compile
          npx nx run twenty-emails:lingui:compile
          npx nx run twenty-front:lingui:compile

      - name: Check and commit compiled files
        id: check_compile_changes
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'
          git add .
          if ! git diff --staged --quiet --exit-code; then
            git commit -m "chore: compile translations"
            echo "changes_detected=true" >> $GITHUB_OUTPUT
          else
            echo "changes_detected=false" >> $GITHUB_OUTPUT
          fi

      - name: Push changes and create remote branch if needed
        if: steps.check_extract_changes.outputs.changes_detected == 'true' || steps.check_compile_changes.outputs.changes_detected == 'true'
        run: git push origin HEAD:i18n

      - name: Upload missing translations
        if: steps.check_extract_changes.outputs.changes_detected == 'true'
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: true
          download_translations: false
          localization_branch_name: i18n
          base_url: 'https://twenty.api.crowdin.com'
        env:
          # A numeric ID, found at https://crowdin.com/project/<projectName>/tools/api
          CROWDIN_PROJECT_ID: 1

          # Visit https://crowdin.com/settings#api-key to create this token
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
      - name: Create a pull request
        if: steps.check_extract_changes.outputs.changes_detected == 'true' || steps.check_compile_changes.outputs.changes_detected == 'true'
        run: |
          if git diff --name-only origin/main..HEAD | grep -q .; then
            gh pr create -B main -H i18n --title 'i18n - translations' --body 'Created by Github action' || true
          else
            echo "No file differences between branches, skipping PR creation"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
