name: <PERSON><PERSON> Shared
on:
  push:
    branches:
      - main

  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        packages/twenty-shared/**
  shared-test:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 30
    runs-on: ubuntu-latest
    env:
      NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
    strategy:
      matrix:
        task: [lint, typecheck, test]
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ github.token }}
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Run ${{ matrix.task }} task
        uses: ./.github/workflows/actions/nx-affected
        with:
          tag: scope:frontend
          tasks: ${{ matrix.task }}
  ci-shared-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: ubuntu-latest
    needs: [changed-files-check, shared-test]
    steps:
      - name: Fail job if any needs failed
        if: contains(needs.*.result, 'failure')
        run: exit 1
