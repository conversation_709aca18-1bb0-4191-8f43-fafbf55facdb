name: CI Chrome Extension
on:
  push:
    branches:
      - main

  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        package.json
        packages/twenty-chrome-extension/**
  chrome-extension-build:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 15
    runs-on: ubuntu-latest
    env:
      VITE_SERVER_BASE_URL: http://localhost:3000
      VITE_FRONT_BASE_URL: http://localhost:3001
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ github.token }}
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Chrome Extension / Run build
        run: npx nx build twenty-chrome-extension
  ci-chrome-extension-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: ubuntu-latest
    needs: [changed-files-check, chrome-extension-build]
    steps:
      - name: Fail job if any needs failed
        if: contains(needs.*.result, 'failure')
        run: exit 1
