name: "Release: on merge"
on:
  pull_request:
    types:
      - closed

jobs:
  tag_and_release:
    timeout-minutes: 10
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true && contains(github.event.pull_request.labels.*.name, 'release')
    steps:
      - name: Check PR Author
        id: check_author
        run: |
          if [[ "${{ github.event.pull_request.user.login }}" != "github-actions[bot]" ]]; then
            echo "PR author (${AUTHOR}) is not trusted. Exiting."
            exit 1
          fi

      - name: Checkout
        uses: actions/checkout@v2
        with:
          ref: main

      - name: Get version from PR title
        id: extract_version
        run: |
          VERSION=$(echo "${{ github.event.pull_request.title }}" | sed -n 's/.*Release v\([0-9.]*\).*/\1/p')
          if [ -z "$VERSION" ]; then
            echo "No valid version found in PR title. Exiting."
            exit 1
          fi
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Push new tag
        run: |
          git config --global user.name 'Github Action Deploy'
          git config --global user.email '<EMAIL>'
          git tag v${{ env.VERSION }}
          git push origin v${{ env.VERSION }}

      - uses: release-drafter/release-drafter@v5
        if: contains(github.event.pull_request.labels.*.name, 'create_release')
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag: v${{ env.VERSION }}
