name: "Release: create"
on:
  workflow_dispatch:
    inputs:
      version:
        required: true
        description: Version to release, without the v (e.g. 1.2.3)
      ref:
        default: main
        description: Ref to start the release from (e.g. main, sha)
      create_release:
        type: boolean
        default: true
        description: Create a release after merging the PR

jobs:
  create_pr:
    timeout-minutes: 10
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.ref }}

      - name: Sanitize version
        id: sanitize
        run: |
          echo version=$(echo ${{ github.event.inputs.version }} | sed 's/^v//') >> $GITHUB_OUTPUT

      - name: Update versions
        run: |
          echo ${{ steps.sanitize.outputs.version }} > version.txt

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
            branch: release/${{ steps.sanitize.outputs.version }}
            commit-message: "chore: release v${{ steps.sanitize.outputs.version }}"
            committer: Github Action Deploy <<EMAIL>>
            author: Github Action Deploy <<EMAIL>>
            title: Release v${{ steps.sanitize.outputs.version }}
            labels: |
              release
              ${{ github.event.inputs.create_release == true && 'create_release' || '' }}
