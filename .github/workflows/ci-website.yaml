name: CI Website
on:
  push:
    branches:
      - main

  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        package.json
        packages/twenty-website/**
  website-build:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 10
    runs-on: ubuntu-latest
    services:
      postgres:
        image: twentycrm/twenty-postgres-spilo
        env:
          PGUSER_SUPERUSER: postgres
          PGPASSWORD_SUPERUSER: postgres
          ALLOW_NOSSL: 'true'
          SPILO_PROVIDER: 'local'
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install

      - name: Server / Create DB
        run: PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d postgres -c 'CREATE DATABASE "default";'

      - name: Website / Run migrations
        run: npx nx database:migrate twenty-website
        env:
          DATABASE_PG_URL: postgres://postgres:postgres@localhost:5432/default
      - name: Website / Build Website
        run: npx nx build twenty-website
        env:
          DATABASE_PG_URL: postgres://postgres:postgres@localhost:5432/default
          KEYSTATIC_GITHUB_CLIENT_ID: xxx
          KEYSTATIC_GITHUB_CLIENT_SECRET: xxx
          KEYSTATIC_SECRET: xxx
          NEXT_PUBLIC_KEYSTATIC_GITHUB_APP_SLUG: xxx
  ci-website-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: ubuntu-latest
    needs: [changed-files-check, website-build]
    steps:
      - name: Fail job if any needs failed
        if: contains(needs.*.result, 'failure')
        run: exit 1
