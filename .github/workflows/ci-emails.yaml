name: <PERSON>I Emails
on:
  push:
    branches:
      - main

  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        package.json
        packages/twenty-emails/**
  emails-test:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 10
    runs-on: depot-ubuntu-24.04-8
    env:
      NX_REJECT_UNKNOWN_LOCAL_CACHE: 0
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install dependencies
        uses: ./.github/workflows/actions/yarn-install
      - name: Build twenty-emails
        run: npx nx build twenty-emails
      - name: Run email tests
        run: |
          # Start the email server in the background
          npx nx run twenty-emails:start &
          SERVER_PID=$!
          
          # Wait for server to start
          sleep 20
          
          # Check if server is running
          if ! curl -s http://localhost:4001/preview/test.email > /dev/null; then
            echo "Email server failed to start"
            kill $SERVER_PID
            exit 1
          fi
          
          # Kill the server
          kill $SERVER_PID
  ci-emails-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: depot-ubuntu-24.04-8
    needs: [changed-files-check, emails-test]
    steps:
      - name: Fail job if any needs failed
        if: contains(needs.*.result, 'failure')
        run: exit 1 