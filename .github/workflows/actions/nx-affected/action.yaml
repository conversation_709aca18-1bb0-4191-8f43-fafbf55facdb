name: Nx Affected CI
inputs:
  parallel:
    required: false
    default: '3'
  tag:
    required: false
  tasks:
    required: true
  configuration:
    required: false
    default: 'ci'
  args:
    required: false
runs:
  using: "composite"
  steps:
    - name: Get last successful commit
      uses: nrwl/nx-set-shas@v4
    - name: Run affected command
      shell: bash
      run: npx nx affected --nxBail --configuration=${{ inputs.configuration }} -t=${{ inputs.tasks }} --parallel=${{ inputs.parallel }} --exclude='*,!tag:${{ inputs.tag }}' ${{ inputs.args }}