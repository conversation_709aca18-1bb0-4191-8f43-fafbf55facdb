name: Yarn Install
inputs:
  node-version:
    required: false
    default: '22'

runs:
  using: "composite"
  steps:
    - name: Cache primary key builder
      id: globals
      shell: bash
      run: |
        echo "ACTION_SHELL=bash" >> "${GITHUB_OUTPUT}"
        echo "CACHE_KEY_PREFIX=node_modules-cache-node-${{ inputs.node-version }}-${{ hashFiles('yarn.lock') }}"  >> "${GITHUB_OUTPUT}"
        echo 'PATH_TO_CACHE<<EOF' >> $GITHUB_OUTPUT
        echo "node_modules" >> $GITHUB_OUTPUT
        echo "packages/*/node_modules" >> $GITHUB_OUTPUT
        echo 'EOF' >> $GITHUB_OUTPUT
    - name: Setup Node.js and get yarn cache
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
    - name: Restore node_modules
      id: cache-node-modules
      uses: actions/cache/restore@v4
      with:
        key: v3-${{ steps.globals.outputs.CACHE_KEY_PREFIX }}-${{github.sha}}
        restore-keys: v3-${{ steps.globals.outputs.CACHE_KEY_PREFIX }}-
        path: ${{ steps.globals.outputs.PATH_TO_CACHE }}
    - name: Install Dependencies
      if: ${{ steps.cache-node-modules.outputs.cache-hit != 'true' && steps.cache-node-modules.outputs.cache-matched-key == '' }}
      shell: ${{ steps.globals.outputs.ACTION_SHELL }}
      run: |
        yarn config set enableHardenedMode true
        yarn --immutable --check-cache
    - name: Save cache
      if: ${{ steps.cache-node-modules.outputs.cache-hit != 'true' && steps.cache-node-modules.outputs.cache-matched-key == '' }}
      uses: actions/cache/save@v4
      with:
        key: ${{ steps.cache-node-modules.outputs.cache-primary-key }}
        path: ${{ steps.globals.outputs.PATH_TO_CACHE }}
      