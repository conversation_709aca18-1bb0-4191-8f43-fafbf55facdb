name: Changed files reusable workflow
on:
  workflow_call:
    inputs:
      files:
        required: true
        type: string
    outputs:
      any_changed:
        value: ${{ jobs.changed-files.outputs.any_changed }}

jobs:
  changed-files:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    outputs:
      any_changed: ${{ steps.changed-files.outputs.any_changed }}
    steps:
      - name: Fetch custom Github Actions and base branch history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Check for changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: ${{ inputs.files }}
