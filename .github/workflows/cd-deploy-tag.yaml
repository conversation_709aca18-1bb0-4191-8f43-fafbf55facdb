name: CD deploy tag
on:
  push:
    tags:
      - 'v*'
jobs:
  deploy-tag:
    timeout-minutes: 3
    runs-on: ubuntu-latest
    steps:
      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.TWENTY_INFRA_TOKEN }}
          repository: twentyhq/twenty-infra
          event-type: auto-deploy-tag
          client-payload: '{"github": ${{ toJson(github) }}}' # Passes the entire github context to the downstream workflow
