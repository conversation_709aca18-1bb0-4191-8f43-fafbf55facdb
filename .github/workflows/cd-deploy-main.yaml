name: CD deploy main
on:
  push:
    branches:
      - main
jobs:
  deploy-main:
    timeout-minutes: 3
    runs-on: ubuntu-latest
    steps:
      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.TWENTY_INFRA_TOKEN }}
          repository: twentyhq/twenty-infra
          event-type: auto-deploy-main
          client-payload: '{"github": ${{ toJson(github) }}}' # Passes the entire github context to the downstream workflow
