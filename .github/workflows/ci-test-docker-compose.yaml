name: 'Test Docker Compose'
on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  changed-files-check:
    uses: ./.github/workflows/changed-files.yaml
    with:
      files: |
        packages/twenty-docker/**
        docker-compose.yml
  test:
    needs: changed-files-check
    if: needs.changed-files-check.outputs.any_changed == 'true'
    timeout-minutes: 30
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run compose
        run: |
          echo "Patching docker-compose.yml..."
          # change image to localbuild using yq
          yq eval 'del(.services.server.image)' -i docker-compose.yml
          yq eval '.services.server.build.context = "../../"' -i docker-compose.yml
          yq eval '.services.server.build.dockerfile = "./packages/twenty-docker/twenty/Dockerfile"' -i docker-compose.yml
          yq eval '.services.server.restart = "no"' -i docker-compose.yml

          echo "Setting up .env file..."
          cp .env.example .env
          echo "Generating secrets..."
          echo "# === Randomly generated secrets ===" >>.env
          echo "APP_SECRET=$(openssl rand -base64 32)" >>.env
          echo "PGPASSWORD_SUPERUSER=$(openssl rand -hex 16)" >>.env

          echo "Docker compose up..."
          docker compose up -d || {
              echo "Docker compose failed to start"
              docker compose logs
              exit 1
          }
          docker compose logs db server -f &
          pid=$!

          echo "Waiting for database to start..."
          count=0
          while [ ! $(docker inspect --format='{{.State.Health.Status}}' twenty-db-1) = "healthy" ]; do
              sleep 1;
              count=$((count+1));
              if [ $(docker inspect --format='{{.State.Status}}' twenty-db-1) = "exited" ]; then
                  echo "Database exited"
                  docker compose logs db
                  exit 1
              fi
              if [ $count -gt 300 ]; then
                  echo "Failed to start database after 5 minutes"
                  docker compose logs db
                  exit 1
              fi
              echo "Still waiting for database... (${count}/60)"
          done

          echo "Waiting for server to start..."
          count=0
          while [ ! $(docker inspect --format='{{.State.Health.Status}}' twenty-server-1) = "healthy" ]; do
              sleep 1;
              count=$((count+1));
              if [ $(docker inspect --format='{{.State.Status}}' twenty-server-1) = "exited" ]; then
                  echo "Server exited"
                  docker compose logs server
                  exit 1
              fi
              if [ $count -gt 300 ]; then
                  echo "Failed to start server after 5 minutes"
                  docker compose logs server
                  exit 1
              fi
              echo "Still waiting for server... (${count}/300s)"
          done
        working-directory: ./packages/twenty-docker/
  ci-test-docker-compose-status-check:
    if: always() && !cancelled()
    timeout-minutes: 5
    runs-on: ubuntu-latest
    needs: [changed-files-check, test]
    steps:
      - name: Fail job if any needs failed
        if: contains(needs.*.result, 'failure')
        run: exit 1
