---
name: Report a bug
about: Report a bug or a functional regression
title: 'Ex: In DarkMode, a blank square appears in bottom right corner while scrolling'
labels: ['type: bug']
assignees: ''

---

## Bug Description

A clear and concise description of what the current behavior is.
Please also add **screenshots** of the existing application.

**Example:** 
```
In DarkMode, when scrollbar are displayed (for example on Companies page, with enough companies in the list), we see a blank square in the bottom right corner
[screenshot]
```

## Expected behavior

A clear and concise description of what the expected behavior is.

**Example:** 
```
The blank square should be transparent (invisible)
```

## Technical inputs

**Example:** 
```
- We are displaying custom scrollbars that disappear when the user is not scrolling. See ScrollWrapper.
- Probably fixable with CSS
```
