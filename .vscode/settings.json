{"editor.formatOnSave": false, "files.eol": "auto", "[typescript]": {"editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.addMissingImports": "always", "source.organizeImports": "always"}}, "[javascript]": {"editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.addMissingImports": "always", "source.organizeImports": "always"}}, "[typescriptreact]": {"editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.addMissingImports": "always", "source.organizeImports": "always"}}, "[json]": {"editor.formatOnSave": true}, "javascript.format.enable": false, "typescript.format.enable": false, "cSpell.enableFiletypes": ["!javascript", "!json", "!typescript", "!typescriptreact", "md", "mdx"], "cSpell.words": ["twentyhq"], "typescript.preferences.importModuleSpecifier": "non-relative", "search.exclude": {"**/.yarn": true}, "eslint.debug": true, "files.associations": {".cursorrules": "markdown"}, "jestrunner.codeLensSelector": "**/*.{test,spec,integration-spec}.{js,jsx,ts,tsx}"}