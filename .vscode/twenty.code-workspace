{
  "folders": [
    {
      "name": "ROOT",
      "path": "../"
    },
    {
      "name": "packages/twenty-chrome-extension",
      "path": "../packages/twenty-chrome-extension"
    },
    {
      "name": "packages/twenty-docker",
      "path": "../packages/twenty-docker"
    },
    {
      "name": "packages/twenty-front",
      "path": "../packages/twenty-front"
    },
    {
      "name": "packages/twenty-ui",
      "path": "../packages/twenty-ui"
    },
    {
      "name": "packages/twenty-emails",
      "path": "../packages/twenty-emails"
    },
    {
      "name": "packages/twenty-shared",
      "path": "../packages/twenty-shared"
    },
    {
      "name": "packages/twenty-server",
      "path": "../packages/twenty-server"
    },
    {
      "name": "packages/twenty-utils",
      "path": "../packages/twenty-utils"
    },
    {
      "name": "packages/twenty-zapier",
      "path": "../packages/twenty-zapier"
    },
    {
      "name": "tools/eslint-rules",
      "path": "../tools/eslint-rules"
    },
    {
      "name": "packages/twenty-e2e-testing",
      "path": "../packages/twenty-e2e-testing"
    }
  ],
  "settings": {
    "editor.formatOnSave": false,
    "files.eol": "auto",
    "[typescript]": {
      "editor.formatOnSave": false,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.addMissingImports": "always"
      }
    },
    "[javascript]": {
      "editor.formatOnSave": false,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.addMissingImports": "always"
      }
    },
    "[typescriptreact]": {
      "editor.formatOnSave": false,
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.addMissingImports": "always"
      }
    },
    "[json]": {
      "editor.formatOnSave": true
    },
    "javascript.format.enable": false,
    "typescript.format.enable": false,
    "cSpell.enableFiletypes": [
      "!javascript",
      "!json",
      "!typescript",
      "!typescriptreact",
      "md",
      "mdx"
    ],
    "cSpell.words": [
      "twentyhq"
    ],
    "typescript.preferences.importModuleSpecifier": "non-relative",
    "[javascript][typescript][typescriptreact]": {
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit",
        "source.addMissingImports": "always"
      }
    },
    "search.exclude": {
      "**/.yarn": true,
    },
    "files.exclude": {
      "packages/": true
    },
    "jest.runMode": "on-demand",
    "jest.disabledWorkspaceFolders": [
      "ROOT",
      "packages/twenty-zapier",
      "packages/twenty-docker",
      "packages/twenty-utils",
      "packages/twenty-postgres"
    ]
  }
}
